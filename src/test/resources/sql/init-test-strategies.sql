-- 清理现有数据
DELETE FROM trade_log WHERE open_id = 'test-open-id';
DELETE FROM grids WHERE open_id = 'test-open-id';
DELETE FROM grid_strategy WHERE open_id = 'test-open-id';

-- 插入测试策略
INSERT INTO grid_strategy (
    id, open_id, name, code, `interval`, target_price, amount, 
    max_fall, sell_grid, buy_grid, sell_price, buy_price, 
    buy_strategy, sell_strategy, remainder_shares, union_id,
    create_at, update_at, is_delete
) VALUES
(1001, 'test-open-id', '测试策略1', '000001', 100, 1000, 10000, 
 5000, 105, 95, 1050, 950, 1, 1, 0, '',
 NOW(), NOW(), 0),
(1002, 'test-open-id', '测试策略2', '000002', 100, 2000, 20000,
 10000, 105, 95, 2100, 1900, 1, 1, 0, '',
 NOW(), NOW(), 0),
(1003, 'test-open-id', '测试策略3', '000003', 100, 3000, 30000,
 15000, 105, 95, 3150, 2850, 1, 1, 0, '',
 NOW(), NOW(), 0);

-- 插入测试网格
INSERT INTO grids (
    id, strategy_id, grid_type, level, hold_shares,
    theoretical_buy_price, theoretical_buy_shares,
    theoretical_sell_price, theoretical_sell_shares,
    trigger_amount, buy_price, status, create_at,
    update_at, buy_at, open_id, is_delete
) VALUES
(2001, 1001, 1, 1, 100, 950, 100, 1050, 100, 10000, 950, 1,
 NOW(), NOW(), NOW(), 'test-open-id', 0),
(2002, 1001, 1, 2, 200, 900, 200, 1000, 200, 20000, 900, 1,
 NOW(), NOW(), NOW(), 'test-open-id', 0),
(2003, 1002, 1, 1, 150, 1900, 150, 2100, 150, 30000, 1900, 1,
 NOW(), NOW(), NOW(), 'test-open-id', 0);

-- 插入测试交易记录
INSERT INTO trade_log (
    strategy_id, grid_id, grid_type, level, trade_type,
    trade_shares, trade_price, reminder_shares,
    theoretical_price, theoretical_shares, profit,
    trade_at, create_at, open_id, is_delete
) VALUES
-- 策略1的交易记录
(1001, 2001, 1, 1, 1, 100, 950, 0, 950, 100, 0,
 NOW(), NOW(), 'test-open-id', 0),
(1001, 2001, 1, 1, 2, 50, 1050, 50, 1050, 50, 5000,
 NOW(), NOW(), 'test-open-id', 0),
-- 策略2的交易记录
(1002, 2003, 1, 1, 1, 150, 1900, 0, 1900, 150, 0,
 NOW(), NOW(), 'test-open-id', 0),
(1002, 2003, 1, 1, 2, 100, 2100, 50, 2100, 100, 20000,
 NOW(), NOW(), 'test-open-id', 0);