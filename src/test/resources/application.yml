# Test configuration
spring:
  datasource:
    url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    username: sa
    password: sa
    driver-class-name: org.h2.Driver
  sql:
    init:
      mode: always
      platform: h2

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect

# MyBatis Configuration
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  mapper-locations: classpath:mapper/*.xml

# PageHelper Configuration
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true

# Logging Configuration
logging:
  level:
    com.chenminjie.sea: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Performance Test Configuration
performance:
  test:
    concurrent-users: 10
    requests-per-user: 5
    timeout-seconds: 10
    response-time:
      average-max: 1000  # Maximum average response time in milliseconds
      p95-max: 1500     # Maximum P95 response time in milliseconds
      absolute-max: 2000 # Maximum allowed response time in milliseconds