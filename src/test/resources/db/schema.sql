drop table if exists grid_strategy;
CREATE TABLE `grid_strategy` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `open_id` varchar(64) NOT NULL,
    `name` varchar(64) NOT NULL,
    `interval` int unsigned NOT NULL,
    `target_price` int unsigned NOT NULL,
    `amount` int unsigned NOT NULL,
    `max_fall` int unsigned NOT NULL,
    `buy_strategy` int unsigned NOT NULL,
    `sell_strategy` int unsigned NOT NULL,
    `sell_grid` int unsigned DEFAULT NULL,
    `buy_grid` int unsigned DEFAULT NULL,
    `sell_price` int unsigned DEFAULT NULL,
    `buy_price` int unsigned DEFAULT NULL,
    `incremental_buy_ratio` int unsigned DEFAULT NULL,
    `medium_large_switch` tinyint DEFAULT NULL,
    `medium_interval` int unsigned DEFAULT NULL,
    `large_interval` int unsigned DEFAULT NULL,
    `union_id` varchar(64) DEFAULT NULL,
    `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `is_delete` int DEFAULT '0',
    `remainder_shares` int DEFAULT '0',
    `code` varchar(255) DEFAULT '',
    PRIMARY KEY (`id`)
);

drop table if exists grid;
CREATE TABLE `grid` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `strategy_id` int unsigned NOT NULL,
    `grid_type` int NOT NULL,
    `level` int NOT NULL,
    `hold_shares` int NOT NULL,
    `theoretical_buy_price` int NOT NULL,
    `theoretical_buy_shares` int NOT NULL,
    `theoretical_sell_price` int NOT NULL,
    `theoretical_sell_shares` int NOT NULL,
    `buy_price` int NOT NULL,
    `status` int NOT NULL,
    `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `buy_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `open_id` varchar(255) NOT NULL,
    `is_delete` bit(1) DEFAULT b'0',
    `trigger_amount` int DEFAULT NULL,
    PRIMARY KEY (`id`)
);

drop table if exists session;
CREATE TABLE `session` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `session_key` varchar(64) NOT NULL,
    `open_id` varchar(64) NOT NULL,
    `union_id` varchar(64) DEFAULT NULL,
    `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `session_key` (`session_key`)
);

drop table if exists stock;
CREATE TABLE `stock` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `code` varchar(255) DEFAULT NULL,
    `name` varchar(255) DEFAULT NULL,
    `type` int DEFAULT NULL COMMENT '0: stock\n1: fund',
    `market` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code_market` (`code`,`market`),
    KEY `idx_code` (`code`),
    KEY `idx_code_prefix` (`code`(10))
);

drop table if exists trade_log;
CREATE TABLE `trade_log` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `strategy_id` int unsigned NOT NULL,
    `grid_id` int unsigned DEFAULT NULL,
    `grid_type` int NOT NULL,
    `level` int DEFAULT NULL,
    `trade_type` int NOT NULL,
    `trade_shares` int NOT NULL,
    `trade_price` int NOT NULL,
    `reminder_shares` int DEFAULT NULL,
    `theoretical_price` int DEFAULT NULL,
    `theoretical_shares` int DEFAULT NULL,
    `trade_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `open_id` varchar(255) NOT NULL,
    `is_delete` bit(1) DEFAULT b'0',
    `profit` int DEFAULT '0',
    PRIMARY KEY (`id`)
);

drop table if exists flexible_trade_log;
CREATE TABLE `flexible_trade_log`
(
     `id` int unsigned NOT NULL AUTO_INCREMENT,
     `strategy_id` int unsigned NOT NULL,
     `trade_type` int NOT NULL,
     `trade_shares` int NOT NULL,
     `trade_price` int NOT NULL,
     `trade_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
     `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
     `open_id` varchar(255) NOT NULL,
     index(`strategy_id`, `open_id`),
     PRIMARY KEY (`id`)
)