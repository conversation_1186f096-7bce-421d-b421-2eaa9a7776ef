INSERT INTO grid_strategy
(id, open_id, name, `interval`, target_price, amount, max_fall, buy_strategy, sell_strategy, sell_grid, buy_grid,
 sell_price, buy_price, incremental_buy_ratio, medium_large_switch, medium_interval, large_interval, union_id,
 create_at, update_at, is_delete, remainder_shares, code)
VALUES (1, 'abc123', '无买入', 500, 860, 2000000, 60, 1, 0, 0, 0, 0, 0, 5, 1, 1500, 3000, 'xyz789', '2023-02-15 15:30:45',
        '2023-02-15 15:30:45', 0, 500, '510510'),
       (2, 'abc123', '有买入', 500, 860, 2000000, 60, 1, 0, 0, 0, 0, 0, 5, 1, 1500, 3000, 'xyz789', '2023-02-15 15:30:45',
        '2023-02-15 15:30:45', 0, 500, '510510'),
       (3, 'abc123', '测试修改交易记录', 500, 860, 2000000, 60, 1, 0, 0, 0, 0, 0, 5, 1, 1500, 3000, 'xyz789',
        '2023-02-15 15:30:45',
        '2023-02-15 15:30:45', 0, 100, '510510');

INSERT INTO `grid`
(id, strategy_id, grid_type, `level`, hold_shares, theoretical_buy_price, theoretical_buy_shares, theoretical_sell_price,
 theoretical_sell_shares, buy_price, `status`, create_at, update_at, buy_at, open_id, is_delete, trigger_amount)
VALUES (1, 2, 0, 100, 200, 860, 200, 910, 200, 850, 0, NOW(), NOW(), NOW(), 'abc123', 0, 2000000),
       (3, 3, 0, 100, 100, 860, 2300, 903, 2200, 860, 1, NOW(), NOW(), '2019-03-07 00:00:00', 'abc123', 0, 2000000);


INSERT INTO trade_log (strategy_id, grid_id, grid_type, level, trade_type, trade_shares, trade_price, reminder_shares,
                       theoretical_price, theoretical_shares, profit, trade_at, open_id)
VALUES (101, 1011, 0, 80, 0, 500, 4455, 0, 4455, 500, 0, '2018-10-15 00:00:00', 'abc123'),
       (101, 1011, 0, 80, 1, 500, 4752, 0, 4455, 500, 147120, '2018-11-02 00:00:00', 'abc123'),
       (101, 1012, 0, 85, 0, 500, 4586, 0, 4455, 500, 0, '2018-10-11 00:00:00', 'abc123'),
       (101, 1012, 0, 85, 1, 500, 5031, 0, 4455, 500, 221060, '2019-02-19 00:00:00', 'abc123'),
       (101, 1013, 0, 90, 0, 500, 5031, 0, 4455, 500, 0, '2018-09-10 00:00:00', 'abc123'),
       (101, 1013, 0, 90, 1, 500, 5324, 0, 4455, 500, 148840, '2019-02-25 00:00:00', 'abc123'),
       (101, 1014, 0, 95, 0, 400, 5310, 0, 4455, 500, 0, '2018-08-02 00:00:00', 'abc123'),
       (101, 1014, 0, 95, 1, 400, 5598, 0, 4455, 500, 114560, '2019-03-04 00:00:00', 'abc123'),
       (101, 1015, 0, 100, 0, 400, 5590, 0, 4455, 500, 0, '2018-07-26 00:00:00', 'abc123'),
       (101, 1015, 0, 100, 1, 400, 5872, 0, 4455, 500, 102800, '2019-03-07 00:00:00', 'abc123'),
       (102, 1016, 0, 100, 0, 400, 5590, 0, 4455, 500, 0, '2019-03-07 00:00:00', 'abc123'),
       (102, 1016, 0, 100, 1, 400, 5872, 0, 4455, 500, 102800, '2019-03-07 00:00:00', 'abc123'),
       (3, 3, 0, 100, 0, 2300, 860, 0, 860, 2300, 0, '2019-03-07 00:00:00', 'abc123'),
       (3, 3, 0, 100, 1, 2200, 903, 100, 903, 2200, 98900, '2022-03-07 00:00:00', 'abc123');
