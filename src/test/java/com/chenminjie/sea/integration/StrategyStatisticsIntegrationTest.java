//package com.chenminjie.sea.integration;
//
//import com.chenminjie.sea.application.service.StrategyService;
//import com.chenminjie.sea.common.dto.QueryPagedResponse;
//import com.chenminjie.sea.common.dto.StrategyStatisticsResponse;
//import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.jdbc.Sql;
//import org.springframework.transaction.annotation.Transactional;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest
//@Transactional
//public class StrategyStatisticsIntegrationTest {
//
//    @Autowired
//    private StrategyService strategyService;
//
//    @Autowired
//    private StrategyDataService strategyDataService;
//
//    private static final String TEST_OPEN_ID = "test-open-id";
//
//    @Test
//    @Sql("/sql/init-test-strategies.sql")
//    void shouldRetrieveStrategyStatisticsFromDatabase() {
//        // When
//        QueryPagedResponse<StrategyStatisticsResponse> response =
//            strategyService.getStrategyStatistics(TEST_OPEN_ID, 1, 10);
//
//        // Then
//        assertNotNull(response);
//        assertFalse(response.getResult().isEmpty());
//
//        // Verify pagination
//        assertEquals(1, response.getPageNum());
//        assertEquals(10, response.getPageSize());
//
//        // Verify first strategy statistics
//        StrategyStatisticsResponse stats = response.getResult().get(0);
//        assertNotNull(stats.getStrategyId());
//        assertNotNull(stats.getTotalInvestment());
//        assertNotNull(stats.getGridSellCount());
//        assertNotNull(stats.getTotalProfit());
//        assertNotNull(stats.getTotalHoldingShares());
//        assertNotNull(stats.getRetainProfitShares());
//    }
//
//    @Test
//    @Sql("/sql/init-test-strategies.sql")
//    void shouldHandlePaginationCorrectly() {
//        // When requesting first page
//        QueryPagedResponse<StrategyStatisticsResponse> firstPage =
//            strategyService.getStrategyStatistics(TEST_OPEN_ID, 1, 5);
//
//        // When requesting second page
//        QueryPagedResponse<StrategyStatisticsResponse> secondPage =
//            strategyService.getStrategyStatistics(TEST_OPEN_ID, 2, 5);
//
//        // Then
//        assertNotNull(firstPage);
//        assertNotNull(secondPage);
//        assertEquals(5, firstPage.getResult().size());
//        assertTrue(secondPage.getResult().size() <= 5);
//
//        // Verify no duplicate strategies between pages
//        boolean hasDuplicates = firstPage.getResult().stream()
//            .map(StrategyStatisticsResponse::getStrategyId)
//            .anyMatch(id -> secondPage.getResult().stream()
//                .map(StrategyStatisticsResponse::getStrategyId)
//                .anyMatch(id2 -> id.equals(id2)));
//        assertFalse(hasDuplicates);
//    }
//
//    @Test
//    @Sql("/sql/init-empty-strategies.sql")
//    void shouldHandleEmptyDatabase() {
//        // When
//        QueryPagedResponse<StrategyStatisticsResponse> response =
//            strategyService.getStrategyStatistics(TEST_OPEN_ID, 1, 10);
//
//        // Then
//        assertNotNull(response);
//        assertTrue(response.getResult().isEmpty());
//        assertEquals(0, response.getTotalPages());
//    }
//
//    @Test
//    @Sql("/sql/init-test-strategies.sql")
//    void shouldCalculateStatisticsCorrectly() {
//        // When
//        QueryPagedResponse<StrategyStatisticsResponse> response =
//            strategyService.getStrategyStatistics(TEST_OPEN_ID, 1, 10);
//
//        // Then
//        assertFalse(response.getResult().isEmpty());
//        StrategyStatisticsResponse stats = response.getResult().get(0);
//
//        // Verify total investment is sum of all buy transactions
//        assertTrue(stats.getTotalInvestment() >= 0);
//
//        // Verify profit calculation
//        assertTrue(stats.getTotalProfit() >= 0);
//
//        // Verify holding shares calculation
//        assertTrue(stats.getTotalHoldingShares() >= 0);
//
//        // Verify retain profit shares calculation
//        assertTrue(stats.getRetainProfitShares() >= 0);
//    }
//}