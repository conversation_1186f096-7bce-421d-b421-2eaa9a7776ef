package com.chenminjie.sea.domain.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class StrategyModelTest {

    @Test
    public void testCalTriggerAmountOfLevel_equalBuy() {
        StrategyModel strategyModel = StrategyModel.builder()
                .buyStrategy(StrategyModel.EQUAL_BUY_STRATEGY)
                .interval(5)
                .amount(2000)
                .build();

        int level = 90;
        int expected = 2000;

        int actual = strategyModel.calTriggerAmountOfLevel(level);

        assertEquals(expected, actual);
    }

    @Test
    public void testCalTriggerAmountOfLevel_incrementalBuy() {
        StrategyModel strategyModel = StrategyModel.builder()
                .buyStrategy(StrategyModel.INCREMENTAL_BUY_STRATEGY)
                .interval(5)
                .amount(2000)
                .incrementalBuyRatio(5)
                .build();

        int level = 80;
        int expected = 2400;

        int actual = strategyModel.calTriggerAmountOfLevel(level);

        assertEquals(expected, actual);
    }

//    @Test
//    public void testCalTriggerAmountOfLevel_illegalLevel() {
//        StrategyModel strategyModel = StrategyModel.builder()
//                .buyStrategy(StrategyModel.INCREMENTAL_BUY_STRATEGY)
//                .interval(5)
//                .amount(2000)
//                .incrementalBuyRatio(5)
//                .build();
//
//        int level = 81;
//
//        assertThrows(InternalException.class, () -> strategyModel.calTriggerAmountOfLevel(level));
//    }
}