//package com.chenminjie.sea.api;
//
//import com.chenminjie.sea.common.dto.QueryPagedResponse;
//import com.chenminjie.sea.common.dto.StrategyStatisticsResponse;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.jdbc.Sql;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.MvcResult;
//
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest
//@AutoConfigureMockMvc
//public class StrategyStatisticsApiTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    private static final String TEST_OPEN_ID = "test-open-id";
//    private static final String BASE_URL = "/api/v1/strategies/statistics";
//
//    @Test
//    @Sql("/sql/init-test-strategies.sql")
//    void shouldReturnStrategyStatistics() throws Exception {
//        // When
//        MvcResult result = mockMvc.perform(get(BASE_URL)
//                .header("GSA-Session", TEST_OPEN_ID)
//                .param("pageNumber", "1")
//                .param("pageSize", "10")
//                .accept(MediaType.APPLICATION_JSON))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.msg").value("ok"))
//            .andExpect(jsonPath("$.pageNum").value(1))
//            .andExpect(jsonPath("$.pageSize").value(10))
//            .andExpect(jsonPath("$.result").isArray())
//            .andReturn();
//
//        // Then verify response content
//        String content = result.getResponse().getContentAsString();
//        assertNotNull(content);
//        assertTrue(content.contains("totalInvestment"));
//        assertTrue(content.contains("gridSellCount"));
//        assertTrue(content.contains("totalProfit"));
//        assertTrue(content.contains("totalHoldingShares"));
//        assertTrue(content.contains("retainProfitShares"));
//    }
//
//    @Test
//    void shouldReturnErrorForInvalidPageParams() throws Exception {
//        mockMvc.perform(get(BASE_URL)
//                .header("GSA-Session", TEST_OPEN_ID)
//                .param("pageNumber", "0")
//                .param("pageSize", "10")
//                .accept(MediaType.APPLICATION_JSON))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.msg").value("error"))
//            .andExpect(jsonPath("$.result").isEmpty());
//    }
//
//    @Test
//    void shouldReturnErrorForMissingSession() throws Exception {
//        mockMvc.perform(get(BASE_URL)
//                .param("pageNumber", "1")
//                .param("pageSize", "10")
//                .accept(MediaType.APPLICATION_JSON))
//            .andExpect(status().isBadRequest());
//    }
//
//    @Test
//    void shouldReturnErrorForInvalidSession() throws Exception {
//        mockMvc.perform(get(BASE_URL)
//                .header("GSA-Session", "invalid-session")
//                .param("pageNumber", "1")
//                .param("pageSize", "10")
//                .accept(MediaType.APPLICATION_JSON))
//            .andExpect(status().isUnauthorized());
//    }
//
//    @Test
//    @Sql("/sql/init-empty-strategies.sql")
//    void shouldHandleEmptyResult() throws Exception {
//        mockMvc.perform(get(BASE_URL)
//                .header("GSA-Session", TEST_OPEN_ID)
//                .param("pageNumber", "1")
//                .param("pageSize", "10")
//                .accept(MediaType.APPLICATION_JSON))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.msg").value("ok"))
//            .andExpect(jsonPath("$.result").isEmpty())
//            .andExpect(jsonPath("$.totalPages").value(0));
//    }
//}