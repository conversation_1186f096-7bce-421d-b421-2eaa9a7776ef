package com.chenminjie.sea.common.util;

import org.decampo.xirr.Transaction;
import org.decampo.xirr.Xirr;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

class FinanceUtilTest {

    @Test
    void xirr() {
        double[] cash = new double[]{-2241, -2124.64, -2516.86, -2293.69, -2228.17, 2343.8, 2239.2, 2665.7, 2514.75, 2376};
//        double[] cash = new double[]{-2241, -2124.64, -2516.86, -2293.69, -2228.17,2376,2514.75,2665.7,2239.2, 2343.8};
        Date[] dates = new Date[] {
                new Date(2018,6,26),
                new Date(2018,7,2),
                new Date(2018,8,10),
                new Date(2018,9,11),
                new Date(2018,9,15),
//                new Date(2018, 10,2),
//                new Date(2019,1,19),
//                new Date(2019,1,25),
//                new Date(2019,2,4),
                new Date(2019,2,7),
                new Date(2019,2,4),
                new Date(2019,1,25),
                new Date(2019,1,19),
                new Date(2018, 10,2),
        };

        List<Transaction> transactions = new ArrayList<>();

        for (int i = 0; i < cash.length; i++) {
            transactions.add(new Transaction(cash[i], dates[i]));
        }

        double result = new Xirr(transactions).xirr();

        Assert.assertTrue(result - 0.163600547 < 0.0000001);
    }

    @Test
    void xirr_1() {
        double[] cash = new double[]{-1000, -2500, -1000, 5050};
        Date[] dates = new Date[] {
                new Date(2016,0,15),
                new Date(2016,1,8),
                new Date(2016,3,17),
                new Date(2016,7,24),
        };

        List<Transaction> transactions = new ArrayList<>();

        for (int i = 0; i < cash.length; i++) {
            transactions.add(new Transaction(cash[i], dates[i]));
        }

        double result = new Xirr(transactions).xirr();

        Assert.assertTrue(result - 0.2504234711 < 0.0000001);
    }
}