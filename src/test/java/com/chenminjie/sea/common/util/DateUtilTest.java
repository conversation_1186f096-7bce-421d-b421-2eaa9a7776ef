package com.chenminjie.sea.common.util;

import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class DateUtilTest {

    @Test
    void getDaysBetween_return1WhenSameDay() {
        Date date = new Date();

        assertEquals(1, DateUtil.getAbsDaysBetween(date, date));
    }

    @Test
    void getDaysBetween_return2When2DaysDifferent() {
        Date startDate = new Date(2023,9,16);
        Date endDate = new Date(2023,9,18);

        assertEquals(2, DateUtil.getAbsDaysBetween(startDate, endDate));
    }

    @Test
    void getDaysBetween_return3WhenMoreThan2DaysLessThan3Days() {
        Date startDate = new Date(2023,8,16, 16, 0, 0);
        Date endDate = new Date(2023,8,19, 0, 0, 0);

        assertEquals(3, DateUtil.getAbsDaysBetween(startDate, endDate));
    }
}