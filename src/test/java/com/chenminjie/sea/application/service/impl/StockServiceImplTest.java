package com.chenminjie.sea.application.service.impl;

import com.chenminjie.sea.domain.core.financial.FinancialDataService;
import com.chenminjie.sea.domain.model.StockIndexModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class StockServiceImplTest {

    @Mock
    private FinancialDataService financialDataService;

    @InjectMocks
    private StockServiceImpl stockService;

    @Captor
    private ArgumentCaptor<List<StockIndexModel>> indexModelsCaptor;

    private static final String CSV_HEADER = "指数代码,指数名称,发布时间,涨跌幅,收盘点位,今年以来涨跌幅,PE-TTM(当前值),PE-TTM(分位点%),PE-TTM(最小值),PE-TTM(20%分位点值),PE-TTM(50%分位点值),PE-TTM(80%分位点值),PE-TTM(最大正值),PB(当前值),PB(分位点%),PB(最小值),PB(20%分位点值),PB(50%分位点值),PB(80%分位点值),PB(最大正值),PS-TTM(当前值),PS-TTM(分位点%),PS-TTM(最小值),PS-TTM(20%分位点值),PS-TTM(50%分位点值),PS-TTM(80%分位点值),PS-TTM(最大正值),净资产收益率(ROE)(2025Q1),净资产收益率(ROE)(2024Q4),净资产收益率(ROE)(2024Q1),股息率,关注度";
    private static final String CSV_DATA_LINE = "=\"1000015\",港股全指,2000-01-01,=0.004,=22867.74,=0.13996994010453667,=5.2322,=0.6259,=2.3986,=4.5421,=5.0042,=6.4935,=7.9154,=0.3402,=0.3977,=0.1807,=0.3031,=0.3754,=0.4728,=0.5705,=0.2373,=0.2249,=0.1953,=0.2349,=0.3134,=0.4223,=0.5368,,,,=0.06047013528853349,=240";

    // CsvHeaderMapping测试用的类，避免反射调用内部类
    private static class TestCsvHeaderMapping {
        private final String headerLine;
        
        public TestCsvHeaderMapping(String headerLine) {
            this.headerLine = headerLine;
        }
        
        public String getHeaderLine() {
            return headerLine;
        }
    }

    @BeforeEach
    void setUp() {
        // 设置任何需要初始化的内容
    }

    @Test
    @DisplayName("测试完整的CSV文件解析和处理流程")
    void testUpdateStockIndex() throws IOException {
        // given - 准备测试数据
        String csvContent = CSV_HEADER + "\n" + CSV_DATA_LINE + "\n" + 
                           "=\"000001\",上证指数,2020-01-01,=0.005,=3000.00,=0.10,=15.0,=0.5,=10.0,=12.0,=14.0,=16.0,=18.0,=1.5,=0.6,=1.0,=1.2,=1.4,=1.6,=1.8,=1.0,=0.5,=0.8,=0.9,=1.0,=1.1,=1.2,,,,=0.02,=1000";
        
        MultipartFile csvFile = new MockMultipartFile(
            "file.csv", 
            "file.csv", 
            "text/csv", 
            csvContent.getBytes(StandardCharsets.UTF_8)
        );
        
        String metricType = "ewpvo";
        Integer periodYear = 5;
        
        // 设置Mock行为
        doNothing().when(financialDataService).batchUpsertIndex(anyList());
        
        // when - 执行被测试的方法
        stockService.updateStockIndex(csvFile, metricType, periodYear);
        
        // then - 验证结果
        verify(financialDataService).batchUpsertIndex(indexModelsCaptor.capture());
        List<StockIndexModel> capturedModels = indexModelsCaptor.getValue();
        
        assertEquals(2, capturedModels.size());
        
        // 验证第一行数据
        StockIndexModel firstModel = capturedModels.get(0);
        assertEquals("1000015", firstModel.getCode());
        assertEquals("港股全指", firstModel.getName());
        assertEquals(metricType, firstModel.getMetricType());
        assertEquals(periodYear, firstModel.getPeriodYear());
        
        // 验证第二行数据
        StockIndexModel secondModel = capturedModels.get(1);
        assertEquals("000001", secondModel.getCode());
        assertEquals("上证指数", secondModel.getName());
    }

    @Test
    @DisplayName("测试处理空CSV文件")
    void testUpdateStockIndexWithEmptyFile() {
        // given - 准备空CSV文件
        MultipartFile emptyFile = new MockMultipartFile(
            "emptyFile.csv", 
            "emptyFile.csv", 
            "text/csv", 
            "".getBytes(StandardCharsets.UTF_8)
        );
        
        // when & then - 执行并验证是否抛出异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            stockService.updateStockIndex(emptyFile, "ewpvo", 5);
        });
        
        assertEquals("CSV file is empty", exception.getMessage());
        verify(financialDataService, never()).batchUpsertIndex(anyList());
    }

    @Test
    @DisplayName("测试CSV文件只有头部没有数据行")
    void testUpdateStockIndexWithHeaderOnly() throws IOException {
        // given - 准备只有表头的CSV文件
        MultipartFile headerOnlyFile = new MockMultipartFile(
            "headerOnly.csv", 
            "headerOnly.csv", 
            "text/csv", 
            CSV_HEADER.getBytes(StandardCharsets.UTF_8)
        );
        
        // when - 执行被测试的方法
        stockService.updateStockIndex(headerOnlyFile, "ewpvo", 5);
        
        // then - 验证结果
        verify(financialDataService).batchUpsertIndex(indexModelsCaptor.capture());
        List<StockIndexModel> capturedModels = indexModelsCaptor.getValue();
        assertTrue(capturedModels.isEmpty());
    }
    
    @Test
    @DisplayName("测试代码验证 - 只接受字母和数字组成的代码")
    void testCodeValidation() throws IOException {
        // given - 准备包含无效代码的CSV数据
        String csvContent = CSV_HEADER + "\n" + 
                           "=\"A@123\",测试指数A,2020-01-01,=0.005,=3000.00,=0.10,=15.0,=0.5,=10.0,=12.0,=14.0,=16.0,=18.0,=1.5,=0.6,=1.0,=1.2,=1.4,=1.6,=1.8,=1.0,=0.5,=0.8,=0.9,=1.0,=1.1,=1.2,,,,=0.02,=1000\n" +
                           "=\"ABC123\",测试指数B,2020-01-01,=0.005,=3000.00,=0.10,=15.0,=0.5,=10.0,=12.0,=14.0,=16.0,=18.0,=1.5,=0.6,=1.0,=1.2,=1.4,=1.6,=1.8,=1.0,=0.5,=0.8,=0.9,=1.0,=1.1,=1.2,,,,=0.02,=1000";
        
        MultipartFile csvFile = new MockMultipartFile(
            "file.csv", 
            "file.csv", 
            "text/csv", 
            csvContent.getBytes(StandardCharsets.UTF_8)
        );
        
        // when - 执行被测试的方法
        stockService.updateStockIndex(csvFile, "ewpvo", 5);
        
        // then - 验证结果: 只有有效的代码被处理
        verify(financialDataService).batchUpsertIndex(indexModelsCaptor.capture());
        List<StockIndexModel> capturedModels = indexModelsCaptor.getValue();
        
        assertEquals(1, capturedModels.size());
        assertEquals("ABC123", capturedModels.get(0).getCode());
    }
    
    @Test
    @DisplayName("测试异常CSV格式处理能力")
    void testMalformedCsvHandling() throws IOException {
        // given - 准备格式异常的CSV数据
        String malformedContent = CSV_HEADER + "\n" + 
                                "=\"123456\"港股全指"; // 缺少逗号的异常格式
        
        MultipartFile csvFile = new MockMultipartFile(
            "malformed.csv", 
            "malformed.csv", 
            "text/csv", 
            malformedContent.getBytes(StandardCharsets.UTF_8)
        );
        
        // when - 执行被测试的方法
        stockService.updateStockIndex(csvFile, "ewpvo", 5);
        
        // then - 验证结果: 异常格式被优雅处理
        verify(financialDataService).batchUpsertIndex(indexModelsCaptor.capture());
        List<StockIndexModel> capturedModels = indexModelsCaptor.getValue();
        assertTrue(capturedModels.isEmpty());
    }
} 