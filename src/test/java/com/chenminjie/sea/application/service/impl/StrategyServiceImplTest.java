//package com.chenminjie.sea.application.service.impl;
//
//import com.chenminjie.sea.common.dto.QueryPagedResponse;
//import com.chenminjie.sea.common.dto.StrategyStatisticsResponse;
//import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
//import com.chenminjie.sea.domain.model.StrategyModel;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.Map;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//class StrategyServiceImplTest {
//
//    @Mock
//    private StrategyDataService strategyDataService;
//
//    @InjectMocks
//    private StrategyServiceImpl strategyService;
//
//    private StrategyModel mockStrategy;
//    private String openId = "test-open-id";
//
//    @BeforeEach
//    void setUp() {
//        mockStrategy = new StrategyModel();
//        mockStrategy.setId(1L);
//        mockStrategy.setOpenId(openId);
//    }
//
//    @Test
//    void shouldReturnEmptyResponseWhenNoStrategiesExist() {
//        when(strategyDataService.getStrategyByOpenId(openId)).thenReturn(Collections.emptyList());
//
//        QueryPagedResponse<StrategyStatisticsResponse> response = strategyService.getStrategyStatistics(openId, 1, 10);
//
//        assertNotNull(response);
//        assertEquals(0, response.getTotalPages());
//        assertTrue(response.getResult().isEmpty());
//        assertEquals("ok", response.getMsg());
//    }
//
//    @Test
//    void shouldReturnErrorResponseForInvalidPageParams() {
//        QueryPagedResponse<StrategyStatisticsResponse> response = strategyService.getStrategyStatistics(openId, 0, 10);
//
//        assertNotNull(response);
//        assertEquals("error", response.getMsg());
//        assertTrue(response.getResult().isEmpty());
//    }
//
//    @Test
//    void shouldCalculateStatisticsCorrectly() {
//        // Given
//        when(strategyDataService.getStrategyByOpenId(openId)).thenReturn(Arrays.asList(mockStrategy));
//
//        Map<Long, Integer> mockInvestments = new HashMap<>();
//        mockInvestments.put(1L, 1000);
//        when(strategyDataService.getTotalInvestments(any())).thenReturn(mockInvestments);
//
//        Map<Long, Integer> mockSellCounts = new HashMap<>();
//        mockSellCounts.put(1L, 5);
//        when(strategyDataService.getGridSellCounts(any())).thenReturn(mockSellCounts);
//
//        Map<Long, Integer> mockProfits = new HashMap<>();
//        mockProfits.put(1L, 200);
//        when(strategyDataService.getTotalProfits(any())).thenReturn(mockProfits);
//
//        Map<Long, Integer> mockHoldingShares = new HashMap<>();
//        mockHoldingShares.put(1L, 100);
//        when(strategyDataService.getTotalHoldingShares(any())).thenReturn(mockHoldingShares);
//
//        Map<Long, Integer> mockRetainShares = new HashMap<>();
//        mockRetainShares.put(1L, 50);
//        when(strategyDataService.getRetainProfitShares(any())).thenReturn(mockRetainShares);
//
//        // When
//        QueryPagedResponse<StrategyStatisticsResponse> response = strategyService.getStrategyStatistics(openId, 1, 10);
//
//        // Then
//        assertNotNull(response);
//        assertEquals(1, response.getResult().size());
//
//        StrategyStatisticsResponse stats = response.getResult().get(0);
//        assertEquals(1L, stats.getStrategyId());
//        assertEquals(1000, stats.getTotalInvestment());
//        assertEquals(5, stats.getGridSellCount());
//        assertEquals(200, stats.getTotalProfit());
//        assertEquals(100, stats.getTotalHoldingShares());
//        assertEquals(50, stats.getRetainProfitShares());
//    }
//
//    @Test
//    void shouldHandleExceptionGracefully() {
//        when(strategyDataService.getStrategyByOpenId(openId)).thenThrow(new RuntimeException("Test exception"));
//
//        Exception exception = assertThrows(RuntimeException.class, () ->
//            strategyService.getStrategyStatistics(openId, 1, 10));
//
//        assertTrue(exception.getMessage().contains("Test exception"));
//    }
//
//    @Test
//    void shouldHandlePageSizeLimitCorrectly() {
//        QueryPagedResponse<StrategyStatisticsResponse> response = strategyService.getStrategyStatistics(openId, 1, 101);
//
//        assertNotNull(response);
//        assertEquals("error", response.getMsg());
//        assertTrue(response.getResult().isEmpty());
//    }
//
//    @Test
//    void shouldHandleNullValuesGracefully() {
//        when(strategyDataService.getStrategyByOpenId(openId)).thenReturn(Arrays.asList(mockStrategy));
//        when(strategyDataService.getTotalInvestments(any())).thenReturn(Collections.emptyMap());
//        when(strategyDataService.getGridSellCounts(any())).thenReturn(Collections.emptyMap());
//        when(strategyDataService.getTotalProfits(any())).thenReturn(Collections.emptyMap());
//        when(strategyDataService.getTotalHoldingShares(any())).thenReturn(Collections.emptyMap());
//        when(strategyDataService.getRetainProfitShares(any())).thenReturn(Collections.emptyMap());
//
//        QueryPagedResponse<StrategyStatisticsResponse> response = strategyService.getStrategyStatistics(openId, 1, 10);
//
//        assertNotNull(response);
//        assertEquals(1, response.getResult().size());
//
//        StrategyStatisticsResponse stats = response.getResult().get(0);
//        assertEquals(0, stats.getTotalInvestment());
//        assertEquals(0, stats.getGridSellCount());
//        assertEquals(0, stats.getTotalProfit());
//        assertEquals(0, stats.getTotalHoldingShares());
//        assertEquals(0, stats.getRetainProfitShares());
//    }
//
//    @Test
//    void shouldHandleConcurrentTimeoutCorrectly() {
//        when(strategyDataService.getStrategyByOpenId(openId)).thenReturn(Arrays.asList(mockStrategy));
//        when(strategyDataService.getTotalInvestments(any())).thenAnswer(invocation -> {
//            Thread.sleep(1000); // Simulate timeout
//            return Collections.emptyMap();
//        });
//
//        Exception exception = assertThrows(RuntimeException.class, () ->
//            strategyService.getStrategyStatistics(openId, 1, 10));
//
//        assertTrue(exception.getCause() instanceof InterruptedException);
//    }
//
//    @Test
//    void shouldClearPageHelperAfterException() {
//        when(strategyDataService.getStrategyByOpenId(openId)).thenThrow(new RuntimeException("Test exception"));
//
//        assertThrows(RuntimeException.class, () ->
//            strategyService.getStrategyStatistics(openId, 1, 10));
//
//        // Verify next query starts with fresh page settings
//        when(strategyDataService.getStrategyByOpenId(openId)).thenReturn(Collections.emptyList());
//        QueryPagedResponse<StrategyStatisticsResponse> response = strategyService.getStrategyStatistics(openId, 1, 10);
//        assertNotNull(response);
//        assertEquals(0, response.getTotalPages());
//    }
//}