//package com.chenminjie.sea.application.service.impl;
//
//import com.chenminjie.sea.Config;
//import com.chenminjie.sea.application.service.TradeLogService;
//import com.chenminjie.sea.common.dto.UpdateTradeLogRequest;
//import com.chenminjie.sea.common.exception.InternalException;
//import com.chenminjie.sea.common.util.DateUtil;
//import com.chenminjie.sea.domain.core.grid.GridDataService;
//import com.chenminjie.sea.domain.core.grid.TradeLogDataService;
//import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
//import com.chenminjie.sea.domain.model.GridModel;
//import com.chenminjie.sea.domain.model.StrategyModel;
//import com.chenminjie.sea.domain.model.TradeLogModel;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.Import;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Date;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@Import(Config.class)
//@RunWith(SpringRunner.class)
//@SpringBootTest
//class TradeLogServiceImplTest {
//
//    @Autowired
//    TradeLogService tradeLogService;
//
//    @Autowired
//    TradeLogDataService tradeLogDataService;
//
//    @Autowired
//    StrategyDataService strategyDataService;
//
//    @Autowired
//    GridDataService gridDataService;
//
//    @Test
//    void updateTradeLog_updateTradePriceSuccess() {
//        String openId = "abc123";
//        TradeLogModel tradeLogModel = null;
//        List<TradeLogModel> tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                tradeLogModel = model;
//            }
//        }
//
//        UpdateTradeLogRequest request = new UpdateTradeLogRequest();
//        request.setSellAt(tradeLogModel.getTradeAt().getTime());
//        request.setSellPrice(905);
//        request.setSellShares(tradeLogModel.getTradeShares());
//        tradeLogService.updateTradeLog(openId, tradeLogModel.getId(), request);
//
//        tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                tradeLogModel = model;
//            }
//        }
//
//        assertEquals(905, tradeLogModel.getTradePrice());
//        assertEquals(103500, tradeLogModel.getProfit());
//    }
//
//    @Test
//    void updateTradeLog_updateTradeDateSuccess() {
//        String openId = "abc123";
//        TradeLogModel tradeLogModel = null;
//        List<TradeLogModel> tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                tradeLogModel = model;
//            }
//        }
//
//        Date today = new Date();
//        UpdateTradeLogRequest request = new UpdateTradeLogRequest();
//        request.setSellAt(DateUtil.getZeroOfDay(today).getTime());
//        request.setSellPrice(tradeLogModel.getTradePrice());
//        request.setSellShares(tradeLogModel.getTradeShares());
//        tradeLogService.updateTradeLog(openId, tradeLogModel.getId(), request);
//
//        TradeLogModel newTradeLogModel = null;
//        tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                newTradeLogModel = model;
//            }
//        }
//
//        assertEquals(DateUtil.getZeroOfDay(today), newTradeLogModel.getTradeAt());
//        assertEquals(tradeLogModel.getTradePrice(), newTradeLogModel.getTradePrice());
//        assertEquals(tradeLogModel.getTradeShares(), newTradeLogModel.getTradeShares());
//        assertEquals(tradeLogModel.getReminderShares(), newTradeLogModel.getReminderShares());
//    }
//
//    @Test
//    void updateTradeLog_addTradeSharesSuccess() {
//        String openId = "abc123";
//        TradeLogModel tradeLogModel = null;
//        List<TradeLogModel> tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                tradeLogModel = model;
//            }
//        }
//
//        Date today = new Date();
//        UpdateTradeLogRequest request = new UpdateTradeLogRequest();
//        request.setSellAt(DateUtil.getZeroOfDay(today).getTime());
//        request.setSellPrice(tradeLogModel.getTradePrice());
//        request.setSellShares(tradeLogModel.getTradeShares() + tradeLogModel.getReminderShares());
//        tradeLogService.updateTradeLog(openId, tradeLogModel.getId(), request);
//
//        TradeLogModel newTradeLogModel = null;
//        tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                newTradeLogModel = model;
//            }
//        }
//        StrategyModel strategyModel = strategyDataService.getStrategyById(3L, openId);
//        GridModel gridModel = gridDataService.getGrid(tradeLogModel.getGridId());
//
//        assertEquals(DateUtil.getZeroOfDay(today), newTradeLogModel.getTradeAt());
//        assertEquals(tradeLogModel.getTradePrice(), newTradeLogModel.getTradePrice());
//        assertEquals(tradeLogModel.getTradeShares() + tradeLogModel.getReminderShares(), newTradeLogModel.getTradeShares());
//        assertEquals(0, newTradeLogModel.getReminderShares());
//        assertEquals(0, strategyModel.getRemainderShares());
//        assertEquals(0, gridModel.getHoldShares());
//    }
//
//    @Test
//    void updateTradeLog_minusTradeSharesSuccess() {
//        String openId = "abc123";
//        TradeLogModel tradeLogModel = null;
//        List<TradeLogModel> tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                tradeLogModel = model;
//            }
//        }
//
//        Date today = new Date();
//        UpdateTradeLogRequest request = new UpdateTradeLogRequest();
//        request.setSellAt(DateUtil.getZeroOfDay(today).getTime());
//        request.setSellPrice(tradeLogModel.getTradePrice());
//        request.setSellShares(tradeLogModel.getTradeShares() - 100);
//        tradeLogService.updateTradeLog(openId, tradeLogModel.getId(), request);
//
//        TradeLogModel newTradeLogModel = null;
//        tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                newTradeLogModel = model;
//            }
//        }
//        StrategyModel strategyModel = strategyDataService.getStrategyById(3L, openId);
//        GridModel gridModel = gridDataService.getGrid(tradeLogModel.getGridId());
//
//        assertEquals(DateUtil.getZeroOfDay(today), newTradeLogModel.getTradeAt());
//        assertEquals(tradeLogModel.getTradePrice(), newTradeLogModel.getTradePrice());
//        assertEquals(tradeLogModel.getTradeShares() - 100, newTradeLogModel.getTradeShares());
//        assertEquals(tradeLogModel.getReminderShares() + 100, newTradeLogModel.getReminderShares());
//        assertEquals(tradeLogModel.getReminderShares() + 100, strategyModel.getRemainderShares());
//        assertEquals(tradeLogModel.getReminderShares() + 100, gridModel.getHoldShares());
//    }
//
//    @Test
//    void updateTradeLog_illegalSellShares() {
//        String openId = "abc123";
//        TradeLogModel tradeLogModel = null;
//        List<TradeLogModel> tradeLogModelList = tradeLogDataService.getTradeLogByStrategyId(openId, 3L);
//        for (TradeLogModel model : tradeLogModelList) {
//            if (TradeLogModel.TRADE_TYPE_SELL.equals(model.getTradeType())) {
//                tradeLogModel = model;
//            }
//        }
//
//        Date today = new Date();
//        UpdateTradeLogRequest request = new UpdateTradeLogRequest();
//        request.setSellAt(DateUtil.getZeroOfDay(today).getTime());
//        request.setSellPrice(tradeLogModel.getTradePrice());
//        request.setSellShares(100000);
//
//        Long id = tradeLogModel.getId();
//        assertThrows(InternalException.class, () -> tradeLogService.updateTradeLog(openId, id, request) );
//    }
//}