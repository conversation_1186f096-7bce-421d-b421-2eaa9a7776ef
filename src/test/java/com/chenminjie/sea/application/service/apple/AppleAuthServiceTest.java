package com.chenminjie.sea.application.service.apple;

import com.chenminjie.sea.application.dto.apple.AppleLoginRequest;
import com.chenminjie.sea.application.dto.apple.AppleLoginResponse;
import com.chenminjie.sea.common.util.JwtUtil;
import com.chenminjie.sea.domain.core.apple.AppleAuthDataService;
import com.chenminjie.sea.domain.core.apple.AppleTokenValidationService;
import com.chenminjie.sea.domain.core.apple.dto.AppleTokenPayload;
import com.chenminjie.sea.domain.model.apple.AppleUserModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 苹果授权服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class AppleAuthServiceTest {
    
    @Mock
    private AppleTokenValidationService appleTokenValidationService;
    
    @Mock
    private AppleAuthDataService appleAuthDataService;
    
    @Mock
    private JwtUtil jwtUtil;
    
    @InjectMocks
    private com.chenminjie.sea.application.service.apple.impl.AppleAuthServiceImpl appleAuthService;
    
    private AppleLoginRequest request;
    private AppleTokenPayload tokenPayload;
    private AppleUserModel userModel;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        request = new AppleLoginRequest();
        request.setIdentityToken("mock-identity-token");
        request.setFullName("John Doe");
        request.setEmail("<EMAIL>");
        
        tokenPayload = new AppleTokenPayload();
        tokenPayload.setIss("https://appleid.apple.com");
        tokenPayload.setSub("000123.abc123def456.0789");
        tokenPayload.setAud("com.chenminjie.sea");
        tokenPayload.setExp(System.currentTimeMillis() / 1000 + 3600); // 1小时后过期
        tokenPayload.setEmail("<EMAIL>");
        tokenPayload.setEmailVerified("true");
        tokenPayload.setIsPrivateEmail("true");
        tokenPayload.setRealUserStatus(2);
        
        userModel = new AppleUserModel();
        userModel.setId(1L);
        userModel.setAppleUserId("000123.abc123def456.0789");
        userModel.setEmail("<EMAIL>");
        userModel.setFullName("John Doe");
        userModel.setIsPrivateEmail(true);
        userModel.setEmailVerified(true);
        userModel.setRealUserStatus(2);
    }
    
    @Test
    void testLoginSuccess_NewUser() {
        // Given
        when(appleTokenValidationService.validateAndParseToken(anyString())).thenReturn(tokenPayload);
        when(appleAuthDataService.findByAppleUserId(anyString())).thenReturn(null);
        when(appleAuthDataService.findByEmail(anyString())).thenReturn(null);
        when(appleAuthDataService.createUser(any(AppleUserModel.class))).thenReturn(userModel);
        when(jwtUtil.generateAccessToken(anyLong(), anyString())).thenReturn("mock-access-token");
        when(jwtUtil.generateRefreshToken(anyLong(), anyString())).thenReturn("mock-refresh-token");
        
        // When
        AppleLoginResponse response = appleAuthService.login(request);
        
        // Then
        assertNotNull(response);
        assertEquals("mock-access-token", response.getAccessToken());
        assertEquals("mock-refresh-token", response.getRefreshToken());
        assertEquals("Bearer", response.getTokenType());
        assertEquals(604800L, response.getExpiresIn());
        assertFalse(response.getIsNewUser());
        
        assertNotNull(response.getUser());
        assertEquals(1L, response.getUser().getId());
        assertEquals("000123.abc123def456.0789", response.getUser().getAppleUserId());
        assertEquals("<EMAIL>", response.getUser().getEmail());
        
        verify(appleTokenValidationService).validateAndParseToken("mock-identity-token");
        verify(appleAuthDataService).findByAppleUserId("000123.abc123def456.0789");
        verify(appleAuthDataService).createUser(any(AppleUserModel.class));
        verify(jwtUtil).generateAccessToken(1L, "000123.abc123def456.0789");
        verify(jwtUtil).generateRefreshToken(1L, "000123.abc123def456.0789");
    }
    
    @Test
    void testLoginSuccess_ExistingUser() {
        // Given
        when(appleTokenValidationService.validateAndParseToken(anyString())).thenReturn(tokenPayload);
        when(appleAuthDataService.findByAppleUserId(anyString())).thenReturn(userModel);
        when(jwtUtil.generateAccessToken(anyLong(), anyString())).thenReturn("mock-access-token");
        when(jwtUtil.generateRefreshToken(anyLong(), anyString())).thenReturn("mock-refresh-token");

        // When
        AppleLoginResponse response = appleAuthService.login(request);

        // Then
        assertNotNull(response);
        assertEquals("mock-access-token", response.getAccessToken());
        assertEquals("mock-refresh-token", response.getRefreshToken());
        assertFalse(response.getIsNewUser());

        verify(appleTokenValidationService).validateAndParseToken("mock-identity-token");
        verify(appleAuthDataService).findByAppleUserId("000123.abc123def456.0789");
        verify(appleAuthDataService, never()).createUser(any());
        verify(jwtUtil).generateAccessToken(1L, "000123.abc123def456.0789");
    }
} 