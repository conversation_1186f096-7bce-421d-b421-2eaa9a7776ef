package com.chenminjie.sea.application.dto.apple;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 苹果登录请求DTO测试
 */
class AppleLoginRequestTest {
    
    @Test
    void testFullNameField() {
        // 测试fullName字段的getter和setter
        AppleLoginRequest request = new AppleLoginRequest();
        
        // 设置identityToken
        request.setIdentityToken("test-identity-token");
        assertEquals("test-identity-token", request.getIdentityToken());
        
        // 设置fullName
        request.setFullName("张三");
        assertEquals("张三", request.getFullName());
        
        // 设置email
        request.setEmail("<EMAIL>");
        assertEquals("<EMAIL>", request.getEmail());
        
        // 验证toString方法不会抛异常
        assertNotNull(request.toString());
    }
    
    @Test
    void testNullFields() {
        AppleLoginRequest request = new AppleLoginRequest();
        
        // 可选字段可以为null
        assertNull(request.getFullName());
        assertNull(request.getEmail());
        
        // identityToken为null时应该可以获取
        assertNull(request.getIdentityToken());
    }
} 