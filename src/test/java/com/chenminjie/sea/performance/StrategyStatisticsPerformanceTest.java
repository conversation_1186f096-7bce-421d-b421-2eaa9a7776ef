//package com.chenminjie.sea.performance;
//
//import com.chenminjie.sea.application.service.StrategyService;
//import com.chenminjie.sea.common.dto.QueryPagedResponse;
//import com.chenminjie.sea.common.dto.StrategyStatisticsResponse;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.jdbc.Sql;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.*;
//import java.util.stream.IntStream;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest
//public class StrategyStatisticsPerformanceTest {
//
//    @Autowired
//    private StrategyService strategyService;
//
//    private static final String TEST_OPEN_ID = "test-open-id";
//    private static final int CONCURRENT_USERS = 10;
//    private static final int REQUESTS_PER_USER = 5;
//    private static final int TIMEOUT_SECONDS = 10;
//
//    @Test
//    @Sql("/sql/init-test-strategies.sql")
//    void shouldHandleConcurrentRequests() throws InterruptedException {
//        // Create thread pool for concurrent users
//        ExecutorService executorService = Executors.newFixedThreadPool(CONCURRENT_USERS);
//        CountDownLatch latch = new CountDownLatch(CONCURRENT_USERS * REQUESTS_PER_USER);
//        List<Future<Long>> responseTimes = new ArrayList<>();
//
//        // Submit concurrent requests
//        for (int i = 0; i < CONCURRENT_USERS; i++) {
//            for (int j = 0; j < REQUESTS_PER_USER; j++) {
//                Future<Long> future = executorService.submit(() -> {
//                    long startTime = System.currentTimeMillis();
//                    try {
//                        QueryPagedResponse<StrategyStatisticsResponse> response =
//                            strategyService.getStrategyStatistics(TEST_OPEN_ID, 1, 10);
//                        assertNotNull(response);
//                        assertFalse(response.getResult().isEmpty());
//                    } finally {
//                        latch.countDown();
//                    }
//                    return System.currentTimeMillis() - startTime;
//                });
//                responseTimes.add(future);
//            }
//        }
//
//        // Wait for all requests to complete
//        boolean completed = latch.await(TIMEOUT_SECONDS, TimeUnit.SECONDS);
//        assertTrue(completed, "Performance test did not complete within timeout");
//
//        // Calculate statistics
//        List<Long> times = new ArrayList<>();
//        for (Future<Long> future : responseTimes) {
//            try {
//                times.add(future.get());
//            } catch (Exception e) {
//                fail("Request failed: " + e.getMessage());
//            }
//        }
//
//        // Verify performance metrics
//        double avgResponseTime = times.stream().mapToLong(Long::longValue).average().orElse(0.0);
//        long maxResponseTime = times.stream().mapToLong(Long::longValue).max().orElse(0L);
//        long p95ResponseTime = calculateP95(times);
//
//        // Log performance metrics
//        System.out.println("Performance Test Results:");
//        System.out.println("Average Response Time: " + avgResponseTime + "ms");
//        System.out.println("Max Response Time: " + maxResponseTime + "ms");
//        System.out.println("P95 Response Time: " + p95ResponseTime + "ms");
//
//        // Assert performance requirements
//        assertTrue(avgResponseTime < 1000, "Average response time exceeds 1 second");
//        assertTrue(maxResponseTime < 2000, "Max response time exceeds 2 seconds");
//        assertTrue(p95ResponseTime < 1500, "P95 response time exceeds 1.5 seconds");
//
//        executorService.shutdown();
//    }
//
//    @Test
//    @Sql("/sql/init-test-strategies.sql")
//    void shouldHandleLargeDataSet() {
//        // Test with multiple pages
//        IntStream.range(0, 5).forEach(page -> {
//            QueryPagedResponse<StrategyStatisticsResponse> response =
//                strategyService.getStrategyStatistics(TEST_OPEN_ID, page + 1, 20);
//            assertNotNull(response);
//            assertTrue(response.getResult().size() <= 20);
//        });
//    }
//
//    private long calculateP95(List<Long> times) {
//        times.sort(Long::compareTo);
//        int index = (int) Math.ceil(0.95 * times.size()) - 1;
//        return times.get(index);
//    }
//}