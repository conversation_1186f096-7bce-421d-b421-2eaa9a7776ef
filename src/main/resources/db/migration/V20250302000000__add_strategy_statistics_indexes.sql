-- 策略统计相关索引
-- 网格策略表索引
CREATE INDEX IF NOT EXISTS idx_grid_strategy_openid ON grid_strategy(open_id, is_delete);
CREATE INDEX IF NOT EXISTS idx_grid_strategy_query ON grid_strategy(open_id, is_delete, id);

-- 网格表索引
CREATE INDEX IF NOT EXISTS idx_grids_strategy ON grids(strategy_id, status, is_delete);
CREATE INDEX IF NOT EXISTS idx_grids_holding ON grids(strategy_id, hold_shares, status, is_delete);

-- 交易记录表索引
CREATE INDEX IF NOT EXISTS idx_trade_log_strategy ON trade_log(strategy_id, trade_type, is_delete);
CREATE INDEX IF NOT EXISTS idx_trade_log_grid ON trade_log(grid_id, trade_type, is_delete);

-- 说明：
-- 1. idx_grid_strategy_openid：用于按用户查询策略列表
-- 2. idx_grid_strategy_query：用于具体策略查询
-- 3. idx_grids_strategy：用于查询策略下的网格
-- 4. idx_grids_holding：用于统计持仓信息
-- 5. idx_trade_log_strategy：用于统计策略交易信息
-- 6. idx_trade_log_grid：用于统计网格交易信息

-- 性能优化建议：
-- 1. 定期ANALYZE表以更新统计信息
-- 2. 考虑对大表进行分区
-- 3. 适当设置自动收集统计信息的阈值
-- 4. 监控索引使用情况，及时优化低效索引