-- 苹果用户表
CREATE TABLE apple_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    apple_user_id VARCHAR(255) NOT NULL UNIQUE COMMENT '苹果用户唯一标识sub',
    email VARCHAR(255) COMMENT '用户邮箱(可能是隐私邮箱)',
    is_private_email BOOLEAN DEFAULT FALSE COMMENT '是否为苹果隐私邮箱',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    real_user_status INT DEFAULT 0 COMMENT '真实用户状态:0-不支持,1-未知,2-真实用户',
    full_name VARCHAR(100) COMMENT '用户全名',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_apple_user_id (apple_user_id),
    INDEX idx_email (email)
); 