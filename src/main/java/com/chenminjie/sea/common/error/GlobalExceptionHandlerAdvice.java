package com.chenminjie.sea.common.error;

import com.chenminjie.sea.common.exception.InternalException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpClientErrorException;

/**
 * @File: GlobalExceptionHandlerAdvice.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/24 23:17
 **/
@ControllerAdvice
public class GlobalExceptionHandlerAdvice {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorBody> methodArgumentNotValidException(MethodArgumentNotValidException e) {
        // 从异常对象中拿到ObjectError对象
        ObjectError objectError = e.getBindingResult().getAllErrors().get(0);
        ErrorBody body = ErrorBody.builder()
                .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                .detail(objectError.getDefaultMessage()).build();
        return new ResponseEntity<>(body, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpClientErrorException.Unauthorized.class)
    public ResponseEntity<Object> unauthorized(HttpClientErrorException.Unauthorized e) {
        return new ResponseEntity<>(null, new HttpHeaders(), HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler({HttpClientErrorException.Forbidden.class,
            HttpClientErrorException.BadRequest.class,
            HttpClientErrorException.NotFound.class})
    public ResponseEntity<Object> forbidden(HttpClientErrorException e) {
        return new ResponseEntity<>(e.getResponseBodyAsString(), new HttpHeaders(), e.getStatusCode());
    }

    @ExceptionHandler(InternalException.class)
    public ResponseEntity<Object> internalException(InternalException e) {
        return new ResponseEntity<>(e.getErrorBody(), new HttpHeaders(), e.getHttpStatus());
    }
}
