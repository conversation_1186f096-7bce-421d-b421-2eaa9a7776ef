package com.chenminjie.sea.common.error;

/**
 * @File: StrategyErrorCode.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/07/18 21:13
 **/
public enum StrategyErrorCode {

    /**
     * duplicate name
     */
    DUPLICATE_NAME("000", "Duplicate name");

    private final String errorCode;
    private final String errorMsg;

    private static final ErrorModule errorModule = ErrorModule.STRATEGY;

    StrategyErrorCode(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public ErrorBody getErrorBody(String detail) {
        return ErrorBody.builder()
                .code(errorModule.getModuleCode() + this.errorCode)
                .reason(this.errorMsg)
                .detail(detail).build();
    }
}
