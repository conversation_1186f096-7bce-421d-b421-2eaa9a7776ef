package com.chenminjie.sea.common.error;

import lombok.Getter;

/**
 * @File: CommonErrorCode
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/15 22:41
 **/
@Getter
public enum CommonErrorCode {

    /**
     * illegal arguments
     */
    ILLEGAL_ARGUMENTS("000", "Illegal arguments"),
    /**
     * Server is busy
     */
    WX_SERVER_ERROR("001", "Server is busy"),

    INTERNAL_DATA_ERROR("002", "Internal data error"),
    FINANCIAL_SERVER_ERROR("003", "Financial server error");

    private final String errorCode;
    private final String errorMsg;

    private static final ErrorModule errorModule = ErrorModule.COMMON;

    CommonErrorCode(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public ErrorBody getErrorBody(String detail) {
        return ErrorBody.builder()
                .code(errorModule.getModuleCode() + this.errorCode)
                .reason(this.errorMsg)
                .detail(detail).build();
    }
}
