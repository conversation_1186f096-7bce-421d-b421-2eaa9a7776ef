package com.chenminjie.sea.common.error;

/**
 * @File: AuthenticationErrorCode.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/15 22:26
 **/
public enum AuthenticationErrorCode {

    /**
     * unauthorized
     */
    UNAUTHORIZED("000", "Unauthorized"),
    /**
     * illegal session key
     */
    ILLEGAL_SESSION_KEY("001", "Illegal session key");

    private final String errorCode;
    private final String errorMsg;

    private static final ErrorModule errorModule = ErrorModule.AUTHENTICATION;

    AuthenticationErrorCode(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public ErrorBody getErrorBody(String detail) {
        return ErrorBody.builder()
                .code(errorModule.getModuleCode() + this.errorCode)
                .reason(this.errorMsg)
                .detail(detail).build();
    }
}
