package com.chenminjie.sea.common.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @File: ValidDate.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2025/02/28
 **/
@Documented
@Constraint(validatedBy = ValidDateValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidDate {
    String message() default "无效的日期参数";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String yearField() default "year";
    
    String monthField() default "month";
    
    String dayField() default "day";
}