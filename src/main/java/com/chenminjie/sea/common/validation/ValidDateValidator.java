package com.chenminjie.sea.common.validation;

import com.chenminjie.sea.common.dto.QueryTradeLogsByDateRequest;
import org.springframework.beans.BeanWrapperImpl;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.YearMonth;

/**
 * @File: ValidDateValidator.java
 * @author: Chen<PERSON>in<PERSON><PERSON>
 * @create: 2025/02/28
 **/
public class ValidDateValidator implements ConstraintValidator<ValidDate, QueryTradeLogsByDateRequest> {

    @Override
    public void initialize(ValidDate constraintAnnotation) {
    }

    @Override
    public boolean isValid(QueryTradeLogsByDateRequest request, ConstraintValidatorContext context) {
        if (request == null) {
            return false;
        }

        // 检查参数组合是否有效
        if (!request.isValidCombination()) {
            addConstraintViolation(context, "无效的日期参数组合");
            return false;
        }

        // 如果没有提供年份，直接返回true
        if (request.getYear() == null) {
            return true;
        }

        try {
            LocalDate today = LocalDate.now();
            
            // 检查年份是否超过当前年份
            if (request.getYear() > today.getYear()) {
                addConstraintViolation(context, "年份不能超过当前年份");
                return false;
            }

            // 如果只有年份，验证通过
            if (request.getMonth() == null) {
                return true;
            }

//            // 验证年月
//            YearMonth yearMonth = YearMonth.of(request.getYear(), request.getMonth());
//            YearMonth currentYearMonth = YearMonth.from(today);
//            if (yearMonth.isAfter(currentYearMonth)) {
//                addConstraintViolation(context, "日期不能超过当前日期");
//                return false;
//            }

            // 如果没有提供日期，验证通过
//            if (request.getDay() == null) {
//                return true;
//            }
//
//            // 验证完整日期
//            LocalDate date = yearMonth.atDay(request.getDay());
//            if (date.isAfter(today)) {
//                addConstraintViolation(context, "日期不能超过当前日期");
//                return false;
//            }

            return true;
        } catch (DateTimeException e) {
            addConstraintViolation(context, "无效的日期值");
            return false;
        }
    }

    private void addConstraintViolation(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message)
                .addConstraintViolation();
    }
}