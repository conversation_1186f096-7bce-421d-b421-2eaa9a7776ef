package com.chenminjie.sea.common.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @File: CreateGridRequest.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/12 16:51
 **/
@Data
public class CreateGridRequest {

    @NotNull(message = "策略ID不能为空")
    private Long strategyId;

    /**
     * 0: small
     * 1: medium
     * 2: large
     * 3: reminder profit
     */
    @NotNull(message = "网格类型不能为空")
    private Integer gridType;

    @Min(value = 1, message = "网格档位应大于 0")
    @Max(value = 100, message = "网格档位不能大于 1")
    @NotNull(message = "网格档位不能为空")
    private Integer level;

    @Min(value = 0, message = "买入股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE, message = "买入股数超出阈值")
    @NotNull(message = "买入股数不能为空")
    private Integer buyShares;

    @Min(value = 1, message = "理论买入价格应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "理论买入价格超出阈值")
    @NotNull(message = "理论买入价格不能为空")
    private Integer theoreticalBuyPrice;

    @Min(value = 99, message = "理论买入股数应大于等于 100")
    @Max(value = Integer.MAX_VALUE, message = "理论买入股数超出阈值")
    @NotNull(message = "理论买入股数不能为空")
    private Integer theoreticalBuyShares;

    @Min(value = 1, message = "理论卖出价格应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "理论卖出价格超出阈值")
    @NotNull(message = "理论卖出价格不能为空")
    private Integer theoreticalSellPrice;

    @Min(value = -1, message = "理论卖出股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE, message = "理论卖出股数超出阈值")
    @NotNull(message = "理论卖出股数不能为空")
    private Integer theoreticalSellShares;

    @Min(value = 1, message = "买入价格应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "买入价格超出阈值")
    @NotNull(message = "买入价格不能为空")
    private Integer buyPrice;

    @Min(value = 1, message = "买入日期过早")
    @Max(value = Long.MAX_VALUE, message = "买入日期过晚")
    @NotNull(message = "买入日期不能为空")
    private Long buyAt;
}
