package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @File: UpdateStrategyRequest.java
 * @author: ChenMinJie
 * @create: 2023/09/16 15:37
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateStrategyRequest {
    @Length(max = 64, message = "策略名称过长")
    private String name;

    @Length(max = 64, message = "证券代码过长")
    private String code;

    @Min(value = 1, message = "网格大小应大于 0.01")
    @Max(value = 9999, message = "网格大小应小于 99.99")
    private Integer interval; // 以0.01为单位，如500表示5.00%

    @Min(value = 1, message = "首网价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "首网价格过大")
    private Integer targetPrice;

    @Min(value = 1, message = "网格金额应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "网格金额过大")
    private Integer amount;

    @Min(value = 1, message = "压力测试应大于 0")
    @Max(value = 99, message = "压力测试应小于 100")
    private Integer maxFall;

    @Min(value = 0, message = "买入策略不合法")
    @Max(value = 10, message = "买入策略不合法")
    private Integer buyStrategy;

    @Min(value = 1, message = "递增比例应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "递增比例过大")
    private Integer incrementalBuyRatio;

    private Boolean mediumLargeSwitch;

    @Min(value = 1, message = "中网大小应大于 0.01")
    @Max(value = 9999, message = "中网大小应小于 99.99")
    private Integer mediumInterval; // 以0.01为单位

    @Min(value = 1, message = "大网大小应大于 0.01")
    @Max(value = 9999, message = "大网大小应小于 99.99")
    private Integer largeInterval; // 以0.01为单位
}
