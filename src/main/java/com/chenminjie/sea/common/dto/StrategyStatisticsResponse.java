package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Chen<PERSON>in<PERSON>ie
 * @create: 2025/03/02
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StrategyStatisticsResponse {

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 当前总投入金额（从未卖出网格统计）
     */
    private Integer totalInvestment;

    /**
     * 网格卖出次数
     */
    private Integer gridSellCount;

    /**
     * 累计收益（从已卖出网格统计）
     */
    private Integer totalProfit;

    /**
     * 总持有股数
     */
    private Integer totalHoldingShares;

    /**
     * 预留利润股数（已卖出网格中剩余的股数）
     */
    private Integer retainProfitShares;
}