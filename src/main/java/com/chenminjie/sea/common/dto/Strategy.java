package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @File: Strategy.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/06/04 13:38
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Strategy {
    private Long id;

    private String openId;

    private String name;

    private String code;

    private Integer interval;

    private Integer targetPrice;

    private Integer amount;

    private Integer maxFall;

    private Integer sellGrid;

    private Integer buyGrid;

    private Integer sellPrice;

    private Integer buyPrice;

    private Integer buyStrategy;

    private Integer sellStrategy;

    private Integer incrementalBuyRatio;

    private Boolean mediumLargeSwitch;

    private Integer mediumInterval;

    private Integer largeInterval;

    private Integer remainderShares;

    private String unionId;

    private Date createAt;

    private Date updateAt;
}
