package com.chenminjie.sea.common.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @File: UpdateGridRequest.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/06 22:34
 **/

@Data
public class UpdateGridRequest {
    @Min(value = 1, message = "买入价格应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "买入价格超出阈值")
    @NotNull(message = "买入价格不能为空")
    private Integer buyPrice;

    @Min(value = 100, message = "买入股数应大于 100")
    @Max(value = Integer.MAX_VALUE, message = "买入股数超出阈值")
    @NotNull(message = "买入股数不能为空")
    private Integer holdShares;

    @Min(value = 1, message = "买入日期过早")
    @Max(value = Long.MAX_VALUE, message = "买入日期过晚")
    @NotNull(message = "买入日期不能为空")
    private Long buyAt;
}
