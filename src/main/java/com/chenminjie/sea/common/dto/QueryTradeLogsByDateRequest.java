package com.chenminjie.sea.common.dto;

import com.chenminjie.sea.common.validation.ValidDate;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * @File: QueryTradeLogsByDateRequest.java
 * @author: ChenMinJie
 * @create: 2025/02/28
 **/
@Data
@ValidDate(message = "请提供有效的日期参数组合")
public class QueryTradeLogsByDateRequest {
    @Min(value = 2000, message = "年份必须大于等于2000")
    @Max(value = 2100, message = "年份必须小于等于2100")
    private Integer year;

    @Min(value = 1, message = "月份必须在1-12之间")
    @Max(value = 12, message = "月份必须在1-12之间")
    private Integer month;

    @Min(value = 1, message = "日期必须在1-31之间")
    @Max(value = 31, message = "日期必须在1-31之间")
    private Integer day;

    /**
     * 验证参数组合的有效性
     * - year 可以单独存在
     * - month 必须和year一起存在
     * - day 必须和year、month一起存在
*/
    public boolean isValidCombination() {
        if (year == null) {
            return month == null && day == null; // 如果没有year，则month和day都必须为空
        }
        if (month == null) {
            return day == null; // 如果有year但没有month，则day必须为空
        }
        return true; // 有year和month的情况下，day可有可无
    }
}