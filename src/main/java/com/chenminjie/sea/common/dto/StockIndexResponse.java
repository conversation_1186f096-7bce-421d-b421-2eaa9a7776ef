package com.chenminjie.sea.common.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class StockIndexResponse {
    List<StockIndex> success;
    List<String> failed;

    public StockIndexResponse() {
        success = new ArrayList<>();
        failed = new ArrayList<>();
    }

    @Data
    public static class StockIndex {
        private String code;
        private String name;
        private String metricType; // 指标类型
        private Date publishDate; // 发布时间
        private BigDecimal changeRate; // 涨跌幅
        private BigDecimal closePoint; // 收盘点位
        private BigDecimal peTtm5Y; // PE-TTM(5年)
        private BigDecimal peTtmPercentile5Y; // PE-TTM(5年分位点%)
        private BigDecimal pb5Y; // PB(5年)
        private BigDecimal pbPercentile5Y; // PB(5年分位点%)
        private BigDecimal psTtm5Y; // PS-TTM(5年)
        private BigDecimal psTtmPercentile5Y; // PS-TTM(5年分位点%)
        private BigDecimal dividendYield5Y; 
        private BigDecimal peTtm10Y; // PE-TTM(10年)
        private BigDecimal peTtmPercentile10Y; // PE-TTM(10年分位点%)
        private BigDecimal pb10Y; // PB(10年)
        private BigDecimal pbPercentile10Y; // PB(10年分位点%)
        private BigDecimal psTtm10Y; // PS-TTM(10年)
        private BigDecimal psTtmPercentile10Y; // PS-TTM(10年分位点%)
        private BigDecimal dividendYield10Y;
        private Long updateTimestamp;
    }
}
