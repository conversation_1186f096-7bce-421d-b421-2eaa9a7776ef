package com.chenminjie.sea.common.dto;

import java.util.Set;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class StockFilterRequest {
    @JsonProperty("pePercentile5Y")
    private Double pePercentile5Y = 1.;
    @JsonProperty("pePercentile10Y")
    private Double pePercentile10Y = 1.;
    @JsonProperty("pbPercentile5Y")
    private Double pbPercentile5Y = 1.;
    @JsonProperty("pbPercentile10Y")
    private Double pbPercentile10Y = 1.;
    private Set<String> weightingMethods; // Can contain "ewpvo" and/or "mcw"

    public boolean isPeFilterEnabled() {
        return pePercentile5Y != null || pePercentile10Y != null;
    }

    public boolean isPbFilterEnabled() {
        return pbPercentile5Y != null || pbPercentile10Y != null;
    }

    public boolean isValid() {
        // Validate percentile ranges if provided
        if (pePercentile5Y != null && (pePercentile5Y < 0 || pePercentile5Y > 1)) {
            return false;
        }
        if (pePercentile10Y != null && (pePercentile10Y < 0 || pePercentile10Y > 1)) {
            return false;
        }
        if (pbPercentile5Y != null && (pbPercentile5Y < 0 || pbPercentile5Y > 1)) {
            return false;
        }
        if (pbPercentile10Y != null && (pbPercentile10Y < 0 || pbPercentile10Y > 1)) {
            return false;
        }

        // At least one filter type should be enabled
        if (!isPeFilterEnabled() && !isPbFilterEnabled()) {
            return false;
        }

        // At least one weighting method should be selected
        return weightingMethods != null && !weightingMethods.isEmpty() &&
               weightingMethods.stream().allMatch(method -> "ewpvo".equals(method) || "mcw".equals(method));
    }
}