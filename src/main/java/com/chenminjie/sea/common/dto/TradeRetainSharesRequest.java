package com.chenminjie.sea.common.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class TradeRetainSharesRequest {

    @Min(value = 1, message = "交易股数应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "交易股数超出阈值")
    @NotNull(message = "交易股数不能为空")
    private Integer tradeShares;

    @Min(value = 0, message = "交易类型不合法")
    @Max(value = 1, message = "交易类型不合法")
    @NotNull(message = "交易类型不能为空")
    private Integer tradeType;

    @Min(value = 1, message = "交易价格应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "交易价格超出阈值")
    @NotNull(message = "交易价格不能为空")
    private Integer tradePrice;

    @Min(value = 1, message = "交易日期过早")
    @Max(value = Long.MAX_VALUE, message = "交易日期过晚")
    @NotNull(message = "交易日期不能为空")
    private Long tradeAt;
}
