package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @File: QueryStrategyResponse.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/16 22:44
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryStrategyResponse {

    private String msg;

    private Integer pageNum;

    private Integer pageSize;

    private Integer totalPages;

    private List<Strategy> result;
}
