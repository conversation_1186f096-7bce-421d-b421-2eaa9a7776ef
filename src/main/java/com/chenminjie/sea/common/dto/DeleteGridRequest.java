package com.chenminjie.sea.common.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @File: DeleteGridRequest.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/26 11:28
 **/
@Data
public class DeleteGridRequest {

    @Min(value = 1, message = "卖出价格应大于 0")
    @Max(value = Integer.MAX_VALUE, message = "卖出价格超出阈值")
    @NotNull(message = "卖出价格不能为空")
    private Integer sellPrice;

    @Min(value = 0, message = "卖出股数应大于等于 0")
    @Max(value = Integer.MAX_VALUE, message = "卖出股数超出阈值")
    @NotNull(message = "卖出股数不能为空")
    private Integer sellShares;

    @Min(value = 1, message = "卖出日期过早")
    @Max(value = Long.MAX_VALUE, message = "卖出日期过晚")
    @NotNull(message = "卖出日期不能为空")
    private Long sellAt;

    /**
     * 是否是删除请求
     * true: 删除请求
     * false or null: 卖出请求
     */
    private Boolean isDelete;
}
