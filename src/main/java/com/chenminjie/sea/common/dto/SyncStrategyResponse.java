package com.chenminjie.sea.common.dto;

import com.chenminjie.sea.application.dto.GridDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncStrategyResponse {

    private String msg;

    private List<SyncStrategy> result;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncStrategy {
        private Long id;
        private String name;
        private String code;
        private int interval;
        private int targetPrice;
        private int amount;
        private int maxFall;
        private int buyStrategy;
        private Integer incrementalBuyRatio;
        private boolean mediumLargeSwitch;
        private Integer mediumInterval;
        private Integer largeInterval;
        private List<SyncGrid> grids;
        private List<SyncGrid> soldGrids;
        private Date createAt;
        private Date updateAt;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncGrid {
        private Long id;
        private Long strategyId;
        private int gridType;
        private int grade;
        private int holdShares;
        private int theoreticalBuyPrice;
        private int theoreticalBuyShares;
        private int theoreticalSellPrice;
        private int theoreticalSellShares;
        private int triggerAmount;
        private Integer status = null;
        private List<SyncTradeLog> tradeLogs;
        private Date createAt;
        private Date updateAt;
        private Date buyAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncTradeLog {
        private Long id;
        private Long strategyId;
        private Long gridId;
        private int tradeType;
        private int tradeShares;
        private int tradePrice;
        private Date tradeAt;
        private Date createAt;
    }

    public static SyncStrategyResponse from(QueryStrategyResponse response, Map<Long, List<GridDto>> gridMap, Map<Long, List<TradeLogDto>> tradeLogMap) {
        SyncStrategyResponse res = new SyncStrategyResponse();
        res.msg = response.getMsg();

        List<SyncStrategy> syncStrategies = new ArrayList<>();
        for (Strategy strategy : response.getResult()) {
            SyncStrategy syncStrategy = new SyncStrategy();
            BeanUtils.copyProperties(strategy, syncStrategy);

            List<SyncGrid> syncGrids = new ArrayList<>();
            List<SyncGrid> soldGrids = new ArrayList<>();
            for (GridDto gridDto : gridMap.getOrDefault(strategy.getId(), new ArrayList<>())) {
                SyncGrid syncGrid = new SyncGrid();
                BeanUtils.copyProperties(gridDto, syncGrid);
                syncGrid.grade = gridDto.getLevel();

                List<SyncTradeLog> syncTradeLogs = new ArrayList<>();
                for (TradeLogDto tradeLogDto : tradeLogMap.getOrDefault(gridDto.getId(), new ArrayList<>())) {
                    SyncTradeLog syncTradeLog = new SyncTradeLog();
                    BeanUtils.copyProperties(tradeLogDto, syncTradeLog);
                    syncTradeLogs.add(syncTradeLog);
                }

                syncGrid.setTradeLogs(syncTradeLogs);
                if (gridDto.getIsDelete()) {
                    soldGrids.add(syncGrid);
                } else {
                    syncGrids.add(syncGrid);
                }
            }

            syncStrategy.setGrids(syncGrids);
            syncStrategy.setSoldGrids(soldGrids);

            syncStrategies.add(syncStrategy);
        }

        res.result = syncStrategies;
        return res;
    }
}
