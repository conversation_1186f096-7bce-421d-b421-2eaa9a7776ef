package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @File: QueryPagedResponse.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/02 10:56
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryPagedResponse<T> {

    private String msg;

    private Integer pageNum;

    private Integer pageSize;

    private Integer totalPages;

    private List<T> result;
}
