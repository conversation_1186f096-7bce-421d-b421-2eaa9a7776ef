package com.chenminjie.sea.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.util.HashSet;
import java.util.Set;

/**
 * @File: CreateStrategyRequest.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/06/04 13:08
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateStrategyRequest {

    @Length(max = 64, message = "策略名称过长")
    @NotBlank(message = "策略名称不能为空")
    private String name;

    @Length(max = 64, message = "证券代码过长")
    private String code;

    @Min(value = 1, message = "网格大小应大于 0.01")
    @Max(value = 9999, message = "网格大小应小于 99.99")
    @NotNull(message = "网格大小不能为空")
    private Integer interval; // 以0.01为单位，如500表示5.00%

    @Min(value = 1, message = "首网价格应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "首网价格过大")
    @NotNull(message = "首网价格不能为空")
    private Integer targetPrice;

    @Min(value = 1, message = "网格金额应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "网格金额过大")
    @NotNull(message = "网格金额不能为空")
    private Integer amount;

    @Min(value = 1, message = "压力测试应大于 0")
    @Max(value = 99, message = "压力测试应小于 100")
    @NotNull(message = "压力测试不能为空")
    private Integer maxFall;

    @Min(value = 0, message = "买入策略不合法")
    @Max(value = 10, message = "买入策略不合法")
    @NotNull(message = "买入策略不能为空")
    private Integer buyStrategy;

    @Min(value = 1, message = "递增比例应大于 0")
    @Max(value = Integer.MAX_VALUE / 2, message = "递增比例过大")
    private Integer incrementalBuyRatio;

    @NotNull(message = "中网大网不能为空")
    private Boolean mediumLargeSwitch;

    @Min(value = 1, message = "中网大小应大于 0.01")
    @Max(value = 9999, message = "中网大小应小于 99.99")
    private Integer mediumInterval; // 以0.01为单位

    @Min(value = 1, message = "大网大小应大于 0.01")
    @Max(value = 9999, message = "大网大小应小于 99.99")
    private Integer largeInterval; // 以0.01为单位

    public static void main(String[] args) {
        Set<Integer> rows = new HashSet<>();

    }
}
