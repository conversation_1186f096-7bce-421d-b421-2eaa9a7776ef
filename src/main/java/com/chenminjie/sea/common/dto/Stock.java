package com.chenminjie.sea.common.dto;

import com.chenminjie.sea.domain.model.StockModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @File: Stock.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/13 19:56
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Stock {

    private String code;

    private String name;

    public static Stock from(StockModel model) {
        Stock stock = new Stock();
        String code = model.getCode();
        if (model.getType().equals(StockModel.TYPE_STOCK)) {
            code = code + "." + model.getMarket().toUpperCase();
        }
        stock.setCode(code);
        stock.setName(model.getName());
        return stock;
    }
}
