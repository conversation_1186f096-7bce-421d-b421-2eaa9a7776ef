package com.chenminjie.sea.common.exception;

import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.error.ErrorBody;
import lombok.Data;
import org.springframework.http.HttpStatus;

/**
 * @File: InternalException.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/07/18 21:17
 **/
@Data
public class InternalException extends RuntimeException {
    private ErrorBody errorBody;
    private HttpStatus httpStatus;

    public InternalException(ErrorBody errorBody, Throwable cause, HttpStatus httpStatus) {
        super(cause);
        this.errorBody = errorBody;
        this.httpStatus = httpStatus;
    }

    public InternalException(CommonErrorCode errorCode, String detail, Throwable cause, HttpStatus httpStatus) {
        super(cause);
        this.errorBody = ErrorBody.builder()
                .code(errorCode.getErrorCode())
                .reason(errorCode.getErrorMsg())
                .detail(detail).build();
        this.httpStatus = httpStatus;
    }
}
