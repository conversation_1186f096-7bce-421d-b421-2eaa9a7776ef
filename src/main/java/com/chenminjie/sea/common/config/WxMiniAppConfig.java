package com.chenminjie.sea.common.config;

import cn.binarywang.wx.miniapp.api.WxMaSecCheckService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaSecCheckServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @File: WxMiniAppConfig.java
 * @author: ChenMinJie
 * @create: 2023/08/08 22:29
 **/
@Configuration
public class WxMiniAppConfig {
    @Value("${wechat.grid.appid}")
    private String appId;

    @Value("${wechat.grid.secret}")
    private String appSecret;

    @Bean
    public WxMaService wxMaService(WxMaConfig wxMaConfig) {
        WxMaService maService = new WxMaServiceImpl();
        maService.addConfig(wxMaConfig.getAppid(), wxMaConfig);
        return maService;
    }

    @Bean
    public WxMaConfig wxMaConfig() {
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(appId);
        config.setSecret(appSecret);
        return config;
    }

    @Bean
    public WxMaSecCheckService wxMaSecCheckService(WxMaService wxMaService) {
        return new WxMaSecCheckServiceImpl(wxMaService);
    }
}
