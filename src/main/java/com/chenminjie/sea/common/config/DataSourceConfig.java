package com.chenminjie.sea.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * @File: DataSourceConfig.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/23 20:15
 **/
@Configuration
public class DataSourceConfig {

    @Bean
    public DataSourceTransactionManager transactionManager(@Autowired DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
