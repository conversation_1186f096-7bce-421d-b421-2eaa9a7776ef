package com.chenminjie.sea.common.util;

/**
 * @File: UserContext.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/10 22:07
 **/
public class UserContext {
    private static final ThreadLocal<String> SESSION_KEY = new ThreadLocal<>();
    private static final ThreadLocal<String> OPEN_ID = new ThreadLocal<>();

    public static String getSessionKey() {
        return SESSION_KEY.get();
    }

    public static void setSessionKey(String sessionKey) {
        SESSION_KEY.set(sessionKey);
    }

    public static String getOpenId() {
        return OPEN_ID.get();
    }

    public static void setOpenId(String openId) {
        OPEN_ID.set(openId);
    }
}
