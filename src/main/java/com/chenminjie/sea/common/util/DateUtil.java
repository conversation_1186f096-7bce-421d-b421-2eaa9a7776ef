package com.chenminjie.sea.common.util;

import java.util.Calendar;
import java.util.Date;

/**
 * @File: DateUtil.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/21 23:29
 **/
public class DateUtil {

    private static final Integer ONE_DAY = 24 * 60 * 60 * 1000;

    public static int getAbsDaysBetween(Date date1, Date date2) {
        long diff = Math.abs(date2.getTime() - date1.getTime());

        int days = (int) (diff / ONE_DAY);
        int remainder = (int) (diff % ONE_DAY);
        if (remainder > 0) {
            days++;
        }

        if (days == 0) {
            days = 1;
        }

        return days;
    }

    public static boolean isSameDay(Date date1, Date date2) {
        if (date1.getYear() == date2.getYear() && date1.getMonth() == date2.getMonth() && date1.getDay() == date2.getDay()) {
            return true;
        }
        return false;
    }

    public static Date getZeroOfDay(Date date) {
        long time = date.getTime();

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }
}
