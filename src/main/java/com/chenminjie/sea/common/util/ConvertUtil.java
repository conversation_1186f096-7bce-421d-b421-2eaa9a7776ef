package com.chenminjie.sea.common.util;

import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.exception.InternalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;

/**
 * @File: ConvertUtil.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/09 22:58
 **/

@Slf4j
public class ConvertUtil {

    public static <T> T convert(Object obj, Class<T> clazz) {
        T t = null;
        try {
            t = clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("convert error, obj: {}, clazz: {}", obj, clazz, e);
            throw new InternalException(CommonErrorCode.INTERNAL_DATA_ERROR, "内部错误", new RuntimeException("Internal Data Error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        BeanUtils.copyProperties(obj, t);
        return t;
    }
}
