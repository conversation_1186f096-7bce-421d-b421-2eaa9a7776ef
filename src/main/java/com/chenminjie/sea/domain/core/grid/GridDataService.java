package com.chenminjie.sea.domain.core.grid;

import com.chenminjie.sea.domain.model.GridModel;

import java.util.List;
import java.util.Set;

/**
 * @File: GridDataService.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/12 17:32
 **/
public interface GridDataService {

    GridModel createGrid(GridModel gridModel);

    void sellUpdateGrid(GridModel gridModel);

    List<GridModel> getGrids(String openId, Long strategyId);

    List<GridModel> getAllGrids(String openId, Long strategyId);

    List<GridModel> getGrids(String openId, Set<Long> gridIds);

    GridModel getGrid(String openId, Long strategyId, Integer level, Integer gridType);

    GridModel getGrid(Long gridId);

    int getValidGridCount(String openId, Long strategyId);

    int deleteByStrategyId(Long strategyId, String openId);

    int deleteById(Long gridId, String openId);

    void updateGrid(GridModel gridModel);

    int setGridTriggerAmount(Long girdId, String openId, Integer triggerAmount);
}
