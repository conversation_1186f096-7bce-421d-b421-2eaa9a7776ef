package com.chenminjie.sea.domain.core.financial;

import com.chenminjie.sea.domain.model.StockIndexModel;
import com.chenminjie.sea.domain.model.StockModel;

import java.util.List;

/**
 * @File: FinancialDataService.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/13 08:21
 **/
public interface FinancialDataService {

    int batchUpsert(List<StockModel> stockModelList);

    List<StockModel> searchByCode(String codePrefix);

    List<StockModel> queryStocksByCodes(List<String> codes);

    List<StockIndexModel> getIndexByCodes(List<String> codes, String metricType, Integer periodYear);

    List<StockIndexModel> searchStockIndex(String codePrefix, String metricType, Integer periodYear);

    void batchUpsertIndex(List<StockIndexModel> indexModels);
    
    /**
     * 根据五年和十年百分位条件筛选股票指标 (旧版本)
     * @param metricType 指标类型
     * @param fiveYearPercentile 五年百分位阈值
     * @param tenYearPercentile 十年百分位阈值
     * @return 符合条件的股票指标列表
     * @deprecated 使用 filterByMultiplePercentiles 代替
     */
    @Deprecated
    List<StockIndexModel> filterByPercentile(String metricType, Double fiveYearPercentile, Double tenYearPercentile);

    /**
     * 根据PE和PB的五年和十年百分位条件筛选股票指标
     * @param metricType 指标类型 ("ewpvo" 或 "mcw")
     * @param pePercentile5Y PE五年百分位阈值
     * @param pePercentile10Y PE十年百分位阈值
     * @param pbPercentile5Y PB五年百分位阈值
     * @param pbPercentile10Y PB十年百分位阈值
     * @return 符合条件的股票指标列表
     */
    List<StockIndexModel> filterByMultiplePercentiles(
            String metricType,
            Double pePercentile5Y,
            Double pePercentile10Y,
            Double pbPercentile5Y,
            Double pbPercentile10Y
    );
}
