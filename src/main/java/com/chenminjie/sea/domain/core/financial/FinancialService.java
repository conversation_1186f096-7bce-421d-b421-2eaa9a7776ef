package com.chenminjie.sea.domain.core.financial;

import com.chenminjie.sea.domain.core.financial.dto.*;

import java.util.Set;

/**
 * @File: FinancialService.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/12 22:23
 **/
public interface FinancialService {

    String GET_FUNDS_API_URL = "https://api.autostock.cn/v1/fund/all";
    String GET_STOCKS_API_URL = "https://api.autostock.cn/v1/stock/all";
    String GET_INDEXES_API_URL = "https://api.autostock.cn/v1/stock/index/all";

    String GET_PRICE_API_URL = "https://api.autostock.cn/v1/stock/min";

    QueryFundsResponse queryFunds(QueryRequest request);

    QueryStocksResponse queryStocks(QueryRequest request);

    QueryIndexsResponse queryIndexs(QueryRequest request);

    QueryPriceResponse queryPrice(String code);

    Set<QueryPriceResponse> queryStocksPrice(Set<String> codes);
}
