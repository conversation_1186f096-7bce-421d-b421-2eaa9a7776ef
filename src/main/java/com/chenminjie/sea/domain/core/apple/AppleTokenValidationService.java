package com.chenminjie.sea.domain.core.apple;

import com.chenminjie.sea.domain.core.apple.dto.AppleTokenPayload;

/**
 * 苹果Token验证服务接口
 */
public interface AppleTokenValidationService {
    
    /**
     * 验证Apple Identity Token
     * @param identityToken 苹果返回的identity token
     * @return 验证结果
     */
    boolean validateToken(String identityToken);
    
    /**
     * 解析Apple Identity Token
     * @param identityToken 苹果返回的identity token
     * @return token负载信息
     */
    AppleTokenPayload parseToken(String identityToken);
    
    /**
     * 验证并解析Apple Identity Token
     * @param identityToken 苹果返回的identity token
     * @return token负载信息，验证失败返回null
     */
    AppleTokenPayload validateAndParseToken(String identityToken);
} 