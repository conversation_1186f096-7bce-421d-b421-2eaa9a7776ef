package com.chenminjie.sea.domain.core.apple.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 苹果JWT Token负载
 */
@Data
public class AppleTokenPayload {
    
    /**
     * 签发者，固定为 "https://appleid.apple.com"
     */
    private String iss;
    
    /**
     * 用户唯一标识
     */
    private String sub;
    
    /**
     * 应用Bundle ID
     */
    private String aud;
    
    /**
     * 过期时间戳
     */
    private Long exp;
    
    /**
     * 签发时间戳
     */
    private Long iat;
    
    /**
     * 授权时间戳
     */
    @JsonProperty("auth_time")
    private Long authTime;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 邮箱是否已验证
     */
    @JsonProperty("email_verified")
    private String emailVerified;
    
    /**
     * 是否为苹果隐私邮箱
     */
    @JsonProperty("is_private_email")
    private String isPrivateEmail;
    
    /**
     * 真实用户状态
     */
    @JsonProperty("real_user_status")
    private Integer realUserStatus;
    
    /**
     * 是否支持nonce
     */
    @JsonProperty("nonce_supported")
    private Boolean nonceSupported;
    
    /**
     * nonce值
     */
    private String nonce;
    
    /**
     * c_hash值
     */
    @JsonProperty("c_hash")
    private String cHash;
    
    /**
     * 检查邮箱是否已验证
     */
    public boolean isEmailVerified() {
        return "true".equalsIgnoreCase(emailVerified);
    }
    
    /**
     * 检查是否为隐私邮箱
     */
    public boolean isPrivateEmailAddress() {
        return "true".equalsIgnoreCase(isPrivateEmail);
    }
    
    /**
     * 检查token是否过期
     */
    public boolean isExpired() {
        if (exp == null) {
            return true;
        }
        return exp < System.currentTimeMillis() / 1000;
    }
    
    /**
     * 验证签发者
     */
    public boolean isValidIssuer() {
        return "https://appleid.apple.com".equals(iss);
    }
} 