package com.chenminjie.sea.domain.core.session.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @File: Code2SessionResponse.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/18 23:21
 **/
@Data
public class Code2SessionResponse {

    @JsonProperty("session_key")
    private String sessionKey;

    @JsonProperty("unionid")
    private String unionID;

    @JsonProperty("errmsg")
    private String errMsg;

    @JsonProperty("openid")
    private String openID;

    @JsonProperty("errcode")
    private int errCode;
}
