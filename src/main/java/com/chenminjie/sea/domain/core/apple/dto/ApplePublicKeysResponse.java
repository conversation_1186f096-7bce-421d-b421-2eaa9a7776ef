package com.chenminjie.sea.domain.core.apple.dto;

import lombok.Data;

import java.util.List;

/**
 * 苹果公钥API响应
 */
@Data
public class ApplePublicKeysResponse {
    
    /**
     * 公钥列表
     */
    private List<ApplePublicKey> keys;
    
    @Data
    public static class ApplePublicKey {
        
        /**
         * 密钥类型，通常为"RSA"
         */
        private String kty;
        
        /**
         * 密钥ID
         */
        private String kid;
        
        /**
         * 用途，通常为"sig"
         */
        private String use;
        
        /**
         * 算法，通常为"RS256"
         */
        private String alg;
        
        /**
         * RSA公钥模数
         */
        private String n;
        
        /**
         * RSA公钥指数
         */
        private String e;
    }
} 