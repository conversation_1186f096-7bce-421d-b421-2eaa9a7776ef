package com.chenminjie.sea.domain.core.apple;

import com.chenminjie.sea.domain.model.apple.AppleUserModel;

/**
 * 苹果授权数据服务接口
 */
public interface AppleAuthDataService {
    
    /**
     * 根据苹果用户ID查询用户
     */
    AppleUserModel findByAppleUserId(String appleUserId);
    
    /**
     * 根据邮箱查询用户
     */
    AppleUserModel findByEmail(String email);
    
    /**
     * 创建新用户
     */
    AppleUserModel createUser(AppleUserModel userModel);
    
    /**
     * 更新用户信息
     */
    AppleUserModel updateUser(AppleUserModel userModel);
    
    /**
     * 根据ID查询用户
     */
    AppleUserModel findById(Long id);
} 