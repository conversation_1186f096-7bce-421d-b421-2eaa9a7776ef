package com.chenminjie.sea.domain.core.strategy;

import com.chenminjie.sea.domain.model.StrategyModel;

import java.util.List;
import java.util.Map;

/**
 * @File: StrategyDataService
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/25 22:21
 * @update: 2025/03/02
 **/
public interface StrategyDataService {

    List<StrategyModel> getStrategyByOpenId(String openId);

    StrategyModel createStrategy(StrategyModel strategyModel);

    void deleteById(Long id, String openId);

    void addRemainderShares(String openId, Long strategyId, Integer addShares);

    StrategyModel getStrategyById(Long id, String openId);

    StrategyModel queryForUpdate(Long id, String openId);

    int updateStrategy(StrategyModel strategyModel);

    /**
     * 获取策略的总投入金额（从未卖出网格统计）
     *
     * @param strategyIds 策略ID列表
     * @return 策略ID -> 总投入金额的映射
     */
    Map<Long, Integer> getTotalInvestments(List<Long> strategyIds);

    /**
     * 获取策略的网格卖出次数
     *
     * @param strategyIds 策略ID列表
     * @return 策略ID -> 卖出次数的映射
     */
    Map<Long, Integer> getGridSellCounts(List<Long> strategyIds);

    /**
     * 获取策略的累计收益（从已卖出网格统计）
     *
     * @param strategyIds 策略ID列表
     * @return 策略ID -> 累计收益的映射
     */
    Map<Long, Integer> getTotalProfits(List<Long> strategyIds);

    /**
     * 获取策略的总持有股数
     *
     * @param strategyIds 策略ID列表
     * @return 策略ID -> 总持有股数的映射
     */
    Map<Long, Integer> getTotalHoldingShares(List<Long> strategyIds);

    /**
     * 获取策略的预留利润股数（已卖出网格中剩余的股数）
     *
     * @param strategyIds 策略ID列表
     * @return 策略ID -> 预留利润股数的映射
     */
    Map<Long, Integer> getRetainProfitShares(List<Long> strategyIds);
}
