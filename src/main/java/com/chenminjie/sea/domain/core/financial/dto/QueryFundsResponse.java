package com.chenminjie.sea.domain.core.financial.dto;

import lombok.Data;

import java.util.List;

/**
 * @File: QueryFundsResponse.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/12 22:26
 *
 * data demo:
 * {
 *     "code": 200,
 *     "message": "操作成功",
 *     "data": [
 *         [
 *             "000001",
 *             "HXCZHH",
 *             "华夏成长混合",
 *             "混合型",
 *             "HUAXIACHENGZHANGHUNHE"
 *         ],
 *         [
 *             "000003",
 *             "ZHKZZZQA",
 *             "中海可转债债券A",
 *             "债券型",
 *             "ZHONGHAIKEZHUANZHAIZHAIQUANA"
 *         ]
 *     ]
 * }
 **/
@Data
public class QueryFundsResponse {
    private Integer code;
    private String message;
    private List<List<String>> data;
}
