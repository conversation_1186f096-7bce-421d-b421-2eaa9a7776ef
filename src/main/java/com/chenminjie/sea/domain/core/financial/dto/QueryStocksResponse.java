package com.chenminjie.sea.domain.core.financial.dto;

import lombok.Data;

import java.util.List;

/**
 * @File: QueryStocksResponse.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/12 22:31
 *
 * data demo:
 * {
 *     "code": 200,
 *     "message": "操作成功",
 *     "data": [
 *         [
 *             "sh600000",
 *             "浦发银行"
 *         ],
 *         [
 *             "sh600001",
 *             "邯郸钢铁"
 *         ],
 *         [
 *             "sh600002",
 *             "齐鲁石化"
 *         ],
 *         [
 *             "sh600003",
 *             "st东北高"
 *         ],
 *         [
 *             "sh600004",
 *             "白云机场"
 *         ]
 *     ]
 * }
 **/
@Data
public class QueryStocksResponse {

    private Integer code;
    private String message;
    private List<List<String>> data;
}
