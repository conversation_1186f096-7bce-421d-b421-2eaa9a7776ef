package com.chenminjie.sea.domain.core.grid;

import com.chenminjie.sea.domain.model.TradeLogModel;

import java.util.Date;
import java.util.List;

/**
 * @File: TradeLogDataService.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/23 20:32
 **/
public interface TradeLogDataService {

    Long create(TradeLogModel model);

    List<TradeLogModel> getTradeLogByGridId(String openId, Long gridId);

    TradeLogModel getTradeLog(String openId, Long id);

    List<TradeLogModel> getTradeLogByStrategyId(String openId, Long strategyId);

    int deleteByIdAndOpenId(Long id, String openId);

    int deleteByGridIdAndOpenId(Long gridId, String openId);

    void updateTradeLog(TradeLogModel model);

    /**
     * 获取指定日期范围内的有效交易日志
     *
     * @param openId 用户ID
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（不包含）
     * @return 交易日志列表
     */
    List<TradeLogModel> getTradeLogsByDateRange(String openId, Date startDate, Date endDate);
}
