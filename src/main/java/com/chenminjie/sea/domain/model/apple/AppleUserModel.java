package com.chenminjie.sea.domain.model.apple;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 苹果用户领域模型
 */
@Data
public class AppleUserModel {
    
    private Long id;
    
    /**
     * 苹果用户唯一标识
     */
    private String appleUserId;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 是否为苹果隐私邮箱
     */
    private Boolean isPrivateEmail;
    
    /**
     * 邮箱是否已验证
     */
    private Boolean emailVerified;
    
    /**
     * 真实用户状态
     */
    private Integer realUserStatus;
    
    /**
     * 用户全名
     */
    private String fullName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 是否为新用户
     */
    public boolean isNewUser() {
        return id == null;
    }
} 