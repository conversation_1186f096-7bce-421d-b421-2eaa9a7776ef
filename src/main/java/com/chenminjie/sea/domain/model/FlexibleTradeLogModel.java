package com.chenminjie.sea.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlexibleTradeLogModel {
    public static final Integer TRADE_TYPE_BUY = 0;
    public static final Integer TRADE_TYPE_SELL = 1;

    private Long id;

    private Long strategyId;

    private Integer tradeType;

    private Integer tradeShares;

    private Integer tradePrice;

    private Date tradeAt;

    private Date createAt;

    private String openId;
}
