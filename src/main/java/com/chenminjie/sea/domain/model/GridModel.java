package com.chenminjie.sea.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @File: GridModel.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/09 23:07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GridModel {

    public static final Integer STATUS_HOLD = 0;
    public static final Integer STATUS_SOLD = 1;

    private Long id;

    private Long strategyId;

    private Integer gridType;

    private Integer level;

    private Integer holdShares;

    private Integer theoreticalBuyPrice;

    private Integer theoreticalBuyShares;

    private Integer theoreticalSellPrice;

    private Integer theoreticalSellShares;

    private Integer triggerAmount;

    private Integer buyPrice;

    private Integer status;

    private Date createAt;

    private Date updateAt;

    private Date buyAt;

    private String openId;

    private Boolean isDelete;

    public void sell(Integer sellShares) {
        holdShares = holdShares - sellShares;
        status = STATUS_SOLD;
        isDelete = true;
        updateAt = new Date();
    }
}
