package com.chenminjie.sea.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @File: StockModel.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/13 00:01
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockModel {

    public static final Integer TYPE_STOCK = 0;
    public static final Integer TYPE_FUND = 1;

    private Long id;

    private String code;

    private String name;

    /**
     * 0: stock
     * 1: fund
     * 2: index
     */
    private Integer type;

    private String market;

    private String maxValue;

    private String minValue;
}
