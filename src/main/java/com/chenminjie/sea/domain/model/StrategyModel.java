package com.chenminjie.sea.domain.model;

import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.exception.InternalException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.util.Date;

/**
 * @File: StrategyModel.java
 * @author: Chen<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/25 22:22
 **/

@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StrategyModel {

    /**
     * 等额买入
     */
    public static final Integer EQUAL_BUY_STRATEGY = 1;
    /**
     * 递增买入
     */
    public static final Integer INCREMENTAL_BUY_STRATEGY = 2;

    private Long id;

    private String openId;

    private String name;

    private String code;

    private Integer interval;

    private Integer targetPrice;

    private Integer amount;

    private Integer maxFall;

    private Integer sellGrid;

    private Integer buyGrid;

    private Integer sellPrice;

    private Integer buyPrice;

    private Integer buyStrategy;

    private Integer sellStrategy;

    private Integer incrementalBuyRatio;

    private Boolean mediumLargeSwitch;

    private Integer mediumInterval;

    private Integer largeInterval;

    private Integer remainderShares;

    private String unionId;

    private Date createAt;

    private Date updateAt;

    public Integer calTriggerAmountOfLevel(int level) {
//        if (((100 -level) % interval) != 0) {
//            log.error("level illegal, open id: {}, strategy id: {}, level: {}, interval: {}",
//                    this.openId, this.id, level, this.interval);
//            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "档位异常",
//                    new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
//        }

        int times = (100 - level) / interval;
        if (EQUAL_BUY_STRATEGY.equals(this.buyStrategy)) {
            return amount;
        } else if (INCREMENTAL_BUY_STRATEGY.equals(this.buyStrategy)) {
            return amount + amount * this.incrementalBuyRatio * times / 100;
        } else {
            log.error("not supported buy strategy: {}", this.buyStrategy);
            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "买入策略暂不支持",
                    new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }
    }

    // 添加辅助方法用于显示和计算
//    public double getInterval() {
//        return interval == null ? 0.0 : interval / 100.0;
//    }
//
//    public double getMediumInterval() {
//        return mediumInterval == null ? 0.0 : mediumInterval / 100.0;
//    }
//
//    public double getLargeInterval() {
//        return largeInterval == null ? 0.0 : largeInterval / 100.0;
//    }

    // 从百分比设置值的辅助方法
    public void setIntervalFromPercent(double percent) {
        this.interval = (int) Math.round(percent * 100);
    }

    public void setMediumIntervalFromPercent(double percent) {
        this.mediumInterval = (int) Math.round(percent * 100);
    }

    public void setLargeIntervalFromPercent(double percent) {
        this.largeInterval = (int) Math.round(percent * 100);
    }
}
