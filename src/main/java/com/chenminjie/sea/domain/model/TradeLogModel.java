package com.chenminjie.sea.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @File: TradeLogModel.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/23 19:58
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeLogModel {

    public static final Integer TRADE_TYPE_BUY = 0;
    public static final Integer TRADE_TYPE_SELL = 1;

    private Long id;

    private Long strategyId;

    private Long gridId;

    private Integer gridType;

    private Integer level;

    private Integer tradeType;

    private Integer tradeShares;

    private Integer tradePrice;

    private Integer reminderShares;

    private Integer theoreticalPrice;

    private Integer theoreticalShares;

    private Integer profit;

    private Date tradeAt;

    private Date createAt;

    private String openId;

    private Boolean isDelete;
}
