package com.chenminjie.sea.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @File: Session.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/17 23:42
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionModel implements Serializable {

    private String sessionKey;

    private String openId;

    private String unionId;
}
