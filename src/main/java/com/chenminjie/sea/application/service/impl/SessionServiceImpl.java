package com.chenminjie.sea.application.service.impl;

import com.chenminjie.sea.application.dto.SessionDto;
import com.chenminjie.sea.application.service.SessionService;
import com.chenminjie.sea.domain.core.session.SessionDataService;
import com.chenminjie.sea.domain.core.session.WeChatSessionService;
import com.chenminjie.sea.domain.core.session.dto.Code2SessionResponse;
import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
import com.chenminjie.sea.domain.model.CreateSessionModel;
import com.chenminjie.sea.domain.model.SessionModel;
import com.chenminjie.sea.domain.model.StrategyModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * @File: SessionServiceImpl.java
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2023/04/18 23:07
 **/
@Component
public class SessionServiceImpl implements SessionService {

    private final WeChatSessionService weChatSessionService;
    private final SessionDataService sessionDataService;

    @Autowired
    private StrategyDataService strategyDataService;

    @Autowired
    public SessionServiceImpl(WeChatSessionService weChatSessionService, SessionDataService sessionDataService) {
        this.weChatSessionService = weChatSessionService;
        this.sessionDataService = sessionDataService;
    }

    @Override
    public SessionDto createSession(CreateSessionModel createSessionModel) {
        if (StringUtils.hasLength(createSessionModel.getCode())) {
            Code2SessionResponse resp = weChatSessionService.code2Session(createSessionModel.getCode());

            SessionModel preSession = sessionDataService.getSessionByOpenId(resp.getOpenID());
            if (Objects.isNull(preSession)) {
                strategyDataService.createStrategy(StrategyModel.builder()
                        .openId(resp.getOpenID())
                        .name("网格策略例子")
                        .code("")
                        .interval(500)
                        .targetPrice(860)
                        .amount(2000000)
                        .maxFall(60)
                        .buyGrid(100)
                        .buyPrice(860)
                        .buyStrategy(2)
                        .sellStrategy(1)
                        .incrementalBuyRatio(5)
                        .mediumLargeSwitch(true)
                        .mediumInterval(1500)
                        .largeInterval(3000)
                        .build());
            }

            SessionModel sessionModel = SessionModel.builder()
                    .sessionKey(resp.getSessionKey())
                    .openId(resp.getOpenID())
                    .unionId(resp.getUnionID()).build();
            sessionDataService.createSession(sessionModel);
            SessionDto sessionDto = new SessionDto();
            BeanUtils.copyProperties(sessionModel, sessionDto);
            return sessionDto;
        }
        return null;
    }

    @Override
    public SessionDto getSession(String sessionKey) {
        SessionDto sessionDto = new SessionDto();
        SessionModel sessionModel = sessionDataService.getSession(sessionKey);
        BeanUtils.copyProperties(sessionModel, sessionDto);
        return sessionDto;
    }

    @Override
    public SessionModel getSessionByOpenId(String openId) {
        return sessionDataService.getSessionByOpenId(openId);
    }


}
