package com.chenminjie.sea.application.service.gtt.impl;

import com.chenminjie.sea.application.service.gtt.GttSessionService;
import com.chenminjie.sea.domain.core.gtt.GttSessionDataService;
import com.chenminjie.sea.domain.model.gtt.GttSessionModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

@Slf4j
@Component
public class GttSessionServiceImpl implements GttSessionService {
    @Autowired
    private GttSessionDataService gttSessionDataService;

    @Override
    public GttSessionModel createSession(String appleId) {
        String sessionKey = UUID.randomUUID().toString();
        // 获取当前时间并加上一个月
        Date expireAt = new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000L);
        log.info("create session appleId: {}, {}", appleId, expireAt);
        return gttSessionDataService.createSession(sessionKey, appleId, expireAt);
    }

    @Override
    public GttSessionModel getSession(String sessionKey) {
        return gttSessionDataService.getSession(sessionKey);
    }
}
