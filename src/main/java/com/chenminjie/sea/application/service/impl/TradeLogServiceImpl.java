package com.chenminjie.sea.application.service.impl;

import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.application.service.TradeLogService;
import com.chenminjie.sea.common.dto.QueryTradeLogsByDateRequest;
import com.chenminjie.sea.common.dto.UpdateTradeLogRequest;
import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.common.util.DateUtil;
import com.chenminjie.sea.domain.core.grid.GridDataService;
import com.chenminjie.sea.domain.core.grid.TradeLogDataService;
import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
import com.chenminjie.sea.domain.model.GridModel;
import com.chenminjie.sea.domain.model.StrategyModel;
import com.chenminjie.sea.domain.model.TradeLogModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @File: TradeLogServiceImpl.java
 * @author: ChenMinJie
 * @create: 2023/10/09 22:00
 **/
@Slf4j
@Component
public class TradeLogServiceImpl implements TradeLogService {

    @Autowired
    private TradeLogDataService tradeLogDataService;

    @Autowired
    private GridDataService gridDataService;

    @Autowired
    private StrategyDataService strategyDataService;

    @Transactional(readOnly = true)
    @Override
    public List<TradeLogDto> getValidTradeLogsByDate(String openId, QueryTradeLogsByDateRequest request) {
        log.info("Fetching trade logs for user: {}, with date params: year={}, month={}, day={}", 
                openId, request.getYear(), request.getMonth(), request.getDay());

        // 计算查询的日期范围
        DateRange dateRange = calculateDateRange(request);
        
        // 获取指定日期范围内的所有交易日志
        List<TradeLogModel> tradeLogs = tradeLogDataService.getTradeLogsByDateRange(
                openId, dateRange.startDate, dateRange.endDate);

        // 获取用户的有效策略IDs
        Set<Long> validStrategyIds = strategyDataService.getStrategyByOpenId(openId).stream()
                .map(StrategyModel::getId)
                .collect(Collectors.toSet());

//        // 获取需要验证的gridIds
//        Set<Long> gridIds = tradeLogs.stream()
//                .map(TradeLogModel::getGridId)
//                .collect(Collectors.toSet());
//
//        // 获取未删除的grids
//        Set<Long> validGridIds = gridDataService.getGrids(openId, gridIds).stream()
//                .filter(grid -> !grid.getIsDelete())
//                .map(GridModel::getId)
//                .collect(Collectors.toSet());

        // 过滤无效的trade logs并转换为DTO
        return tradeLogs.stream()
                .filter(log -> validStrategyIds.contains(log.getStrategyId()))
//                        && validGridIds.contains(log.getGridId()))
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private DateRange calculateDateRange(QueryTradeLogsByDateRequest request) {
        LocalDate startDate;
        LocalDate endDate;

        if (request.getYear() == null) {
            // 查询所有记录
            startDate = LocalDate.of(2000, 1, 1);
            endDate = LocalDate.now().plusDays(1);
        } else if (request.getMonth() == null) {
            // 查询整年
            startDate = LocalDate.of(request.getYear(), 1, 1);
            endDate = startDate.plusYears(1);
        } else if (request.getDay() == null) {
            // 查询整月
            YearMonth yearMonth = YearMonth.of(request.getYear(), request.getMonth());
            startDate = yearMonth.atDay(1);
            endDate = startDate.plusMonths(1);
        } else {
            // 查询具体日期
            startDate = LocalDate.of(request.getYear(), request.getMonth(), request.getDay());
            endDate = startDate.plusDays(1);
        }

        // 转换为Date类型
        return new DateRange(
                Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant())
        );
    }

    private static class DateRange {
        private final Date startDate;
        private final Date endDate;

        public DateRange(Date startDate, Date endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public void updateTradeLog(String openId, Long tradeLogId, UpdateTradeLogRequest request) {
        TradeLogModel tradeLogModel = tradeLogDataService.getTradeLog(openId, tradeLogId);
        if (request.getSellShares() > (tradeLogModel.getTradeShares() + tradeLogModel.getReminderShares()) || request.getSellShares() <= 0) {
            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "卖出股数应小于等于买入股数且大于零",
                    new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }

        TradeLogModel changedTradeLogModel = TradeLogModel.builder().id(tradeLogModel.getId()).openId(openId).build();
        GridModel gridModel = gridDataService.getGrid(tradeLogModel.getGridId());

        if (request.getSellAt() < gridModel.getBuyAt().getTime()) {
            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "卖出时间应大于买入时间",
                    new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }

        if (!Objects.equals(tradeLogModel.getTradeShares(), request.getSellShares())) {
            int diff = tradeLogModel.getTradeShares() - request.getSellShares();
            StrategyModel strategyModel = strategyDataService.queryForUpdate(gridModel.getStrategyId(), openId);

            gridModel.setUpdateAt(new Date());
            gridModel.setHoldShares(gridModel.getHoldShares() + diff);
            gridDataService.updateGrid(gridModel);

            strategyDataService.updateStrategy(StrategyModel.builder()
                    .remainderShares(strategyModel.getRemainderShares() + diff)
                    .id(strategyModel.getId())
                    .openId(openId).build());
            changedTradeLogModel.setTradeShares(request.getSellShares());
            changedTradeLogModel.setProfit((request.getSellPrice() - gridModel.getBuyPrice()) * (gridModel.getHoldShares() + request.getSellShares()));
            changedTradeLogModel.setReminderShares(tradeLogModel.getReminderShares() + diff);
        }

        if (!Objects.equals(tradeLogModel.getTradePrice(), request.getSellPrice())) {
            changedTradeLogModel.setProfit((request.getSellPrice() - gridModel.getBuyPrice()) * (gridModel.getHoldShares() + request.getSellShares()));
            changedTradeLogModel.setTradePrice(request.getSellPrice());
        }

        if (!Objects.equals(tradeLogModel.getTradeAt().getTime(), request.getSellAt())) {
            changedTradeLogModel.setTradeAt(DateUtil.getZeroOfDay(new Date(request.getSellAt())));
        }

        tradeLogDataService.updateTradeLog(changedTradeLogModel);
    }

    private TradeLogDto convertToDto(TradeLogModel model) {
        if (model == null) {
            return null;
        }

        TradeLogDto dto = new TradeLogDto();
        dto.setId(model.getId());
        dto.setStrategyId(model.getStrategyId());
        dto.setGridId(model.getGridId());
        dto.setGridType(model.getGridType());
        dto.setLevel(model.getLevel());
        dto.setTradeType(model.getTradeType());
        dto.setTradeShares(model.getTradeShares());
        dto.setTradePrice(model.getTradePrice());
        dto.setReminderShares(model.getReminderShares());
        dto.setTheoreticalPrice(model.getTheoreticalPrice());
        dto.setTheoreticalShares(model.getTheoreticalShares());
        dto.setTradeAt(model.getTradeAt());
        dto.setCreateAt(model.getCreateAt());
        dto.setProfit(model.getProfit());

        return dto;
    }
}
