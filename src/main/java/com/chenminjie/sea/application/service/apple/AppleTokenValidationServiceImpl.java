package com.chenminjie.sea.application.service.apple;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chenminjie.sea.domain.core.apple.AppleService;
import com.chenminjie.sea.domain.core.apple.AppleTokenValidationService;
import com.chenminjie.sea.domain.core.apple.dto.ApplePublicKeysResponse;
import com.chenminjie.sea.domain.core.apple.dto.AppleTokenPayload;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.PublicKey;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 苹果Token验证服务实现
 */
@Slf4j
@Service
public class AppleTokenValidationServiceImpl implements AppleTokenValidationService {
    
    @Autowired
    private AppleService appleService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${apple.bundle.id}")
    private String appleBundleId;
    
    private static final String APPLE_ISSUER = "https://appleid.apple.com";
    private static final int PUBLIC_KEY_CACHE_HOURS = 24;
    
    // 本地缓存，存储公钥信息
    private final ConcurrentHashMap<String, CachedPublicKey> publicKeyCache = new ConcurrentHashMap<>();
    
    /**
     * 缓存的公钥信息
     */
    private static class CachedPublicKey {
        private final PublicKey publicKey;
        private final LocalDateTime expiredTime;
        
        public CachedPublicKey(PublicKey publicKey, LocalDateTime expiredTime) {
            this.publicKey = publicKey;
            this.expiredTime = expiredTime;
        }
        
        public PublicKey getPublicKey() {
            return publicKey;
        }
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiredTime);
        }
    }
    
    @Override
    public boolean validateToken(String identityToken) {
        return validateAndParseToken(identityToken) != null;
    }
    
    @Override
    public AppleTokenPayload parseToken(String identityToken) {
        try {
            String[] parts = identityToken.split("\\.");
            if (parts.length != 3) {
                log.error("Invalid JWT format");
                return null;
            }
            
            // 解析payload
            String payload = new String(Base64.getUrlDecoder().decode(parts[1]));
            return objectMapper.readValue(payload, AppleTokenPayload.class);
        } catch (Exception e) {
            log.error("解析Apple identity token失败", e);
            return null;
        }
    }
    
    @Override
    public AppleTokenPayload validateAndParseToken(String identityToken) {
        try {
            // 1. 解析token
            AppleTokenPayload payload = parseToken(identityToken);
            if (payload == null) {
                log.error("解析Apple identity token失败");
                return null;
            }
            
            // 2. 验证基本字段
            if (!validateBasicFields(payload)) {
                return null;
            }
            
            // 3. 验证签名
            if (!validateSignature(identityToken, payload)) {
                return null;
            }
            
            return payload;
        } catch (Exception e) {
            log.error("验证Apple identity token失败", e);
            return null;
        }
    }
    
    /**
     * 验证基本字段
     */
    private boolean validateBasicFields(AppleTokenPayload payload) {
        // 验证issuer
        if (!APPLE_ISSUER.equals(payload.getIss())) {
            log.error("Invalid issuer: {}", payload.getIss());
            return false;
        }
        
        // 验证audience
        if (!appleBundleId.equals(payload.getAud())) {
            log.error("Invalid audience: {}, expected: {}", payload.getAud(), appleBundleId);
            return false;
        }
        
        // 验证过期时间
        if (payload.isExpired()) {
            log.error("Token已过期");
            return false;
        }
        
        // 验证subject
        if (!StringUtils.hasText(payload.getSub())) {
            log.error("Missing subject");
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证签名
     */
    private boolean validateSignature(String identityToken, AppleTokenPayload payload) {
        try {
            // 解析JWT header获取kid
            String header = getJwtHeader(identityToken);
            if (header == null) {
                return false;
            }
            
            Map<String, Object> headerMap = objectMapper.readValue(header, Map.class);
            String kid = (String) headerMap.get("kid");
            
            if (!StringUtils.hasText(kid)) {
                log.error("Missing kid in JWT header");
                return false;
            }
            
            // 获取公钥
            PublicKey publicKey = getPublicKey(kid);
            if (publicKey == null) {
                log.error("无法获取公钥，kid: {}", kid);
                return false;
            }
            
            // 验证签名
            return verifyJwtSignature(identityToken, publicKey);
        } catch (Exception e) {
            log.error("验证签名失败", e);
            return false;
        }
    }
    
    /**
     * 获取JWT header
     */
    private String getJwtHeader(String jwt) {
        try {
            String[] parts = jwt.split("\\.");
            if (parts.length != 3) {
                return null;
            }
            return new String(Base64.getUrlDecoder().decode(parts[0]));
        } catch (Exception e) {
            log.error("解析JWT header失败", e);
            return null;
        }
    }
    
    /**
     * 获取公钥
     */
    private PublicKey getPublicKey(String kid) {
        try {
            // 1. 从本地缓存中获取
            CachedPublicKey cachedKey = publicKeyCache.get(kid);
            if (cachedKey != null && !cachedKey.isExpired()) {
                log.debug("从本地缓存中获取到公钥，kid: {}", kid);
                return cachedKey.getPublicKey();
            }
            
            // 2. 从Apple服务器获取
            ApplePublicKeysResponse response = appleService.getPublicKeys();
            if (response == null || response.getKeys() == null) {
                log.error("无法从Apple服务器获取公钥");
                return null;
            }
            
            // 3. 缓存公钥并查找目标公钥
            ApplePublicKeysResponse.ApplePublicKey targetKey = null;
            for (ApplePublicKeysResponse.ApplePublicKey key : response.getKeys()) {
                cachePublicKey(key);
                if (kid.equals(key.getKid())) {
                    targetKey = key;
                }
            }
            
            if (targetKey == null) {
                log.error("未找到匹配的公钥，kid: {}", kid);
                return null;
            }
            
            return parsePublicKey(targetKey);
        } catch (Exception e) {
            log.error("获取公钥失败", e);
            return null;
        }
    }
    
    /**
     * 缓存公钥到本地
     */
    private void cachePublicKey(ApplePublicKeysResponse.ApplePublicKey key) {
        try {
            PublicKey publicKey = parsePublicKey(key);
            if (publicKey != null) {
                LocalDateTime expiredTime = LocalDateTime.now().plusHours(PUBLIC_KEY_CACHE_HOURS);
                CachedPublicKey cachedKey = new CachedPublicKey(publicKey, expiredTime);
                publicKeyCache.put(key.getKid(), cachedKey);
                log.debug("缓存公钥成功，kid: {}", key.getKid());
            }
        } catch (Exception e) {
            log.error("缓存公钥失败", e);
        }
    }
    
    /**
     * 解析公钥
     */
    private PublicKey parsePublicKey(ApplePublicKeysResponse.ApplePublicKey key) {
        try {
            Map<String, Object> keyMap = new HashMap<>();
            keyMap.put("kty", key.getKty());
            keyMap.put("kid", key.getKid());
            keyMap.put("use", key.getUse());
            keyMap.put("alg", key.getAlg());
            keyMap.put("n", key.getN());
            keyMap.put("e", key.getE());
            
            Jwk jwk = Jwk.fromValues(keyMap);
            return jwk.getPublicKey();
        } catch (JwkException e) {
            log.error("解析公钥失败", e);
            return null;
        }
    }
    
    /**
     * 验证JWT签名
     */
    private boolean verifyJwtSignature(String jwt, PublicKey publicKey) {
        try {
            Jwts.parser()
                    .verifyWith(publicKey)
                    .build()
                    .parseSignedClaims(jwt);
            return true;
        } catch (JwtException e) {
            log.error("JWT签名验证失败", e);
            return false;
        }
    }
} 