package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.common.dto.QueryTradeLogsByDateRequest;
import com.chenminjie.sea.common.dto.UpdateTradeLogRequest;

import java.util.List;

/**
 * @File: TradeLogService.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/09 21:58
 **/
public interface TradeLogService {

    void updateTradeLog(String openId, Long tradeLogId, UpdateTradeLogRequest request);

    /**
     * 获取指定日期范围的有效交易日志
     *
     * @param openId 用户ID
     * @param request 日期查询参数
     * @return 交易日志列表
     */
    List<TradeLogDto> getValidTradeLogsByDate(String openId, QueryTradeLogsByDateRequest request);
}
