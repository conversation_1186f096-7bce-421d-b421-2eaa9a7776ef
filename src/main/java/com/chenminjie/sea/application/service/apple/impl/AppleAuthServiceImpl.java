package com.chenminjie.sea.application.service.apple.impl;

import com.chenminjie.sea.application.dto.apple.*;
import com.chenminjie.sea.application.service.apple.AppleAuthService;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.common.error.CommonErrorCode;
import org.springframework.http.HttpStatus;
import com.chenminjie.sea.common.util.ConvertUtil;
import com.chenminjie.sea.common.util.JwtUtil;
import com.chenminjie.sea.domain.core.apple.AppleAuthDataService;
import com.chenminjie.sea.domain.core.apple.AppleTokenValidationService;
import com.chenminjie.sea.domain.core.apple.dto.AppleTokenPayload;
import com.chenminjie.sea.domain.model.apple.AppleUserModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 苹果授权服务实现
 */
@Slf4j
@Service
public class AppleAuthServiceImpl implements AppleAuthService {
    
    @Autowired
    private AppleTokenValidationService appleTokenValidationService;
    
    @Autowired
    private AppleAuthDataService appleAuthDataService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    @Transactional
    public AppleLoginResponse login(AppleLoginRequest request) {
        try {
            // 1. 验证Apple identity token
            AppleTokenPayload tokenPayload = appleTokenValidationService.validateAndParseToken(request.getIdentityToken());
            if (tokenPayload == null) {
                throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "苹果登录验证失败：identityToken无效", null, HttpStatus.BAD_REQUEST);
            }
            
            log.info("苹果登录验证成功，用户ID: {}", tokenPayload.getSub());
            
            // 2. 查询或创建用户
            AppleUserModel userModel = findOrCreateUser(tokenPayload, request);
            
            // 3. 生成JWT token
            String accessToken = jwtUtil.generateAccessToken(userModel.getId(), userModel.getAppleUserId());
            String refreshToken = jwtUtil.generateRefreshToken(userModel.getId(), userModel.getAppleUserId());
            
            // 4. 构建响应
            AppleUserDto userDto = ConvertUtil.convert(userModel, AppleUserDto.class);
            if (userDto != null) {
                userDto.setFullName(userModel.getFullName());
            }
            
            return AppleLoginResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(60 * 60 * 24 * 7L)
                    .user(userDto)
                    .isNewUser(userModel.isNewUser())
                    .build();
            
        } catch (InternalException e) {
            log.error("苹果登录失败", e);
            throw e;
        } catch (Exception e) {
            log.error("苹果登录系统异常", e);
            throw new InternalException(CommonErrorCode.INTERNAL_DATA_ERROR, "苹果登录系统异常", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 查询或创建用户
     */
    private AppleUserModel findOrCreateUser(AppleTokenPayload tokenPayload, AppleLoginRequest request) {
        String appleUserId = tokenPayload.getSub();
        
        // 1. 根据苹果用户ID查询
        AppleUserModel existingUser = appleAuthDataService.findByAppleUserId(appleUserId);
        if (existingUser != null) {
            // 更新用户信息（如果有新信息）
            return updateUserIfNeeded(existingUser, tokenPayload, request);
        }
        
        // 2. 根据邮箱查询（避免重复账户）
        if (StringUtils.hasText(tokenPayload.getEmail())) {
            AppleUserModel userByEmail = appleAuthDataService.findByEmail(tokenPayload.getEmail());
            if (userByEmail != null) {
                // 更新用户的苹果ID关联
                userByEmail.setAppleUserId(appleUserId);
                return updateUserIfNeeded(userByEmail, tokenPayload, request);
            }
        }
        
        // 3. 创建新用户
        return createNewUser(tokenPayload, request);
    }
    
    /**
     * 创建新用户
     */
    private AppleUserModel createNewUser(AppleTokenPayload tokenPayload, AppleLoginRequest request) {
        AppleUserModel newUser = new AppleUserModel();
        newUser.setAppleUserId(tokenPayload.getSub());
        newUser.setEmail(tokenPayload.getEmail());
        newUser.setIsPrivateEmail(tokenPayload.isPrivateEmailAddress());
        newUser.setEmailVerified(tokenPayload.isEmailVerified());
        newUser.setRealUserStatus(tokenPayload.getRealUserStatus());
        
        // 设置用户姓名（首次登录时客户端提供）
        if (StringUtils.hasText(request.getFullName())) {
            newUser.setFullName(request.getFullName());
        }
        
        // 如果客户端提供了邮箱信息，优先使用客户端的
        if (StringUtils.hasText(request.getEmail())) {
            newUser.setEmail(request.getEmail());
        }
        
        log.info("创建新用户，苹果用户ID: {}, 邮箱: {}", newUser.getAppleUserId(), newUser.getEmail());
        return appleAuthDataService.createUser(newUser);
    }
    
    /**
     * 更新用户信息（如果需要）
     */
    private AppleUserModel updateUserIfNeeded(AppleUserModel existingUser, AppleTokenPayload tokenPayload, 
                                            AppleLoginRequest request) {
        boolean needUpdate = false;
        
        // 更新苹果用户ID（如果之前没有关联）
        if (!tokenPayload.getSub().equals(existingUser.getAppleUserId())) {
            existingUser.setAppleUserId(tokenPayload.getSub());
            needUpdate = true;
        }
        
        // 更新邮箱信息
        if (StringUtils.hasText(tokenPayload.getEmail()) && 
            !tokenPayload.getEmail().equals(existingUser.getEmail())) {
            existingUser.setEmail(tokenPayload.getEmail());
            needUpdate = true;
        }
        
        // 更新邮箱验证状态
        if (tokenPayload.isEmailVerified() != existingUser.getEmailVerified()) {
            existingUser.setEmailVerified(tokenPayload.isEmailVerified());
            needUpdate = true;
        }
        
        // 更新隐私邮箱标识
        if (tokenPayload.isPrivateEmailAddress() != existingUser.getIsPrivateEmail()) {
            existingUser.setIsPrivateEmail(tokenPayload.isPrivateEmailAddress());
            needUpdate = true;
        }
        
        // 更新真实用户状态
        if (tokenPayload.getRealUserStatus() != null && 
            !tokenPayload.getRealUserStatus().equals(existingUser.getRealUserStatus())) {
            existingUser.setRealUserStatus(tokenPayload.getRealUserStatus());
            needUpdate = true;
        }
        
        // 更新用户姓名（如果客户端提供且之前为空）
        if (!StringUtils.hasText(existingUser.getFullName()) && 
            StringUtils.hasText(request.getFullName())) {
            existingUser.setFullName(request.getFullName());
            needUpdate = true;
        }
        
        // 如果有更新，保存到数据库
        if (needUpdate) {
            log.info("更新用户信息，用户ID: {}", existingUser.getId());
            return appleAuthDataService.updateUser(existingUser);
        }
        
        return existingUser;
    }
} 