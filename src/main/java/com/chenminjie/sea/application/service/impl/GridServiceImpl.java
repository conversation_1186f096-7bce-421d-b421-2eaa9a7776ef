package com.chenminjie.sea.application.service.impl;

import com.chenminjie.sea.application.dto.GridDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.application.service.GridService;
import com.chenminjie.sea.common.dto.CreateGridRequest;
import com.chenminjie.sea.common.dto.DeleteGridRequest;
import com.chenminjie.sea.common.dto.UpdateGridRequest;
import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.error.ErrorBody;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.common.util.DateUtil;
import com.chenminjie.sea.common.util.UserContext;
import com.chenminjie.sea.domain.core.grid.GridDataService;
import com.chenminjie.sea.domain.core.grid.TradeLogDataService;
import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
import com.chenminjie.sea.domain.model.GridModel;
import com.chenminjie.sea.domain.model.StrategyModel;
import com.chenminjie.sea.domain.model.TradeLogModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @File: GridServiceImpl.java
 * @author: ChenMinJie
 * @create: 2023/08/12 17:29
 **/
@Slf4j
@Component
public class GridServiceImpl implements GridService {

    @Autowired
    private GridDataService gridDataService;

    @Autowired
    private TradeLogDataService tradeLogDataService;

    @Autowired
    private StrategyDataService strategyDataService;

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class, noRollbackFor = InternalException.class)
    @Override
    public GridDto buyGrid(CreateGridRequest request) {
        StrategyModel strategyModel = strategyDataService.getStrategyById(request.getStrategyId(), UserContext.getOpenId());
        if (Objects.isNull(strategyModel)) {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("当前策略不存在").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }

        int triggerAmount = strategyModel.calTriggerAmountOfLevel(request.getLevel());

        GridModel model = gridDataService.getGrid(
                UserContext.getOpenId(), request.getStrategyId(), request.getLevel(), request.getGridType());
        if (Objects.isNull(model)) {
            GridModel gridModel = new GridModel();
            BeanUtils.copyProperties(request, gridModel);
            gridModel.setBuyAt(new Date(request.getBuyAt()));
            gridModel.setOpenId(UserContext.getOpenId());
            gridModel.setHoldShares(request.getBuyShares());
            gridModel.setTriggerAmount(triggerAmount);
            gridModel.setStatus(0);
            gridModel = gridDataService.createGrid(gridModel);

            TradeLogModel tradeLog = TradeLogModel.builder()
                    .strategyId(gridModel.getStrategyId())
                    .gridId(gridModel.getId())
                    .gridType(gridModel.getGridType())
                    .level(gridModel.getLevel())
                    .tradeType(TradeLogModel.TRADE_TYPE_BUY)
                    .tradeShares(gridModel.getHoldShares())
                    .tradePrice(gridModel.getBuyPrice())
                    .theoreticalPrice(gridModel.getTheoreticalBuyPrice())
                    .theoreticalShares(gridModel.getTheoreticalBuyShares())
                    .tradeAt(gridModel.getBuyAt())
                    .openId(gridModel.getOpenId())
                    .build();
            tradeLogDataService.create(tradeLog);

            GridDto gridDto = new GridDto();
            BeanUtils.copyProperties(gridModel, gridDto);
            return gridDto;
        } else {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("当前网格已存在").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public List<GridDto> getAllGrids(String openId, Long strategyId) {
        List<GridModel> gridModels = gridDataService.getGrids(openId, strategyId);
        List<GridDto> grids = new ArrayList<>(gridModels.size());
        StrategyModel strategyModel = null;
        for (GridModel gridModel : gridModels) {
            if (Objects.isNull(gridModel.getTriggerAmount())) {
                if (Objects.isNull(strategyModel)) {
                    strategyModel = strategyDataService.getStrategyById(gridModel.getStrategyId(), openId);
                    if (Objects.isNull(strategyModel)) {
                        log.error("strategy not found, openId: {}, strategyId: {}", openId, gridModel.getStrategyId());
                        throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "策略不存在",
                                new IllegalArgumentException(), HttpStatus.NOT_FOUND);
                    }
                }
                gridModel.setTriggerAmount(strategyModel.calTriggerAmountOfLevel(gridModel.getLevel()));
            }
            GridDto dto = new GridDto();
            BeanUtils.copyProperties(gridModel, dto);
            grids.add(dto);
        }
        return grids;
    }

    @Override
    public List<GridDto> getAllGridsWithInvalid(String openId, Long strategyId) {
        List<GridModel> gridModels = gridDataService.getAllGrids(openId, strategyId);
        List<GridDto> grids = new ArrayList<>(gridModels.size());
        StrategyModel strategyModel = null;
        for (GridModel gridModel : gridModels) {
            if (gridModel.getIsDelete() && gridModel.getStatus() == 0) {
                continue;
            }
            if (Objects.isNull(gridModel.getTriggerAmount())) {
                if (Objects.isNull(strategyModel)) {
                    strategyModel = strategyDataService.getStrategyById(gridModel.getStrategyId(), openId);
                    if (Objects.isNull(strategyModel)) {
                        log.error("strategy not found, openId: {}, strategyId: {}", openId, gridModel.getStrategyId());
                        throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "策略不存在",
                                new IllegalArgumentException(), HttpStatus.NOT_FOUND);
                    }
                }
                gridModel.setTriggerAmount(strategyModel.calTriggerAmountOfLevel(gridModel.getLevel()));
            }
            GridDto dto = new GridDto();
            BeanUtils.copyProperties(gridModel, dto);
            grids.add(dto);
        }
        return grids;
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public GridDto getGrid(String openId, Long gridId) {
        List<GridModel> gridModels = gridDataService.getGrids(openId, Collections.singleton(gridId));
        if (gridModels.isEmpty()) {
            log.error("grid not found, openId: {}, gridId: {}", openId, gridId);
            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "网格不存在",
                    new IllegalArgumentException(), HttpStatus.NOT_FOUND);
        }

        GridModel gridModel = gridModels.get(0);
        if (Objects.isNull(gridModel.getTriggerAmount())) {
            StrategyModel strategyModel = strategyDataService.getStrategyById(gridModel.getStrategyId(), openId);
            if (Objects.isNull(strategyModel)) {
                log.error("strategy not found, openId: {}, strategyId: {}", openId, gridModel.getStrategyId());
                throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "策略不存在",
                        new IllegalArgumentException(), HttpStatus.NOT_FOUND);
            }
            gridModel.setTriggerAmount(strategyModel.calTriggerAmountOfLevel(gridModel.getLevel()));
            gridDataService.setGridTriggerAmount(gridId, openId, gridModel.getTriggerAmount());
        }

        GridDto dto = new GridDto();
        BeanUtils.copyProperties(gridModel, dto);
        return dto;
    }

    @Override
    public void updateGrid(String openId, Long gridId, UpdateGridRequest request) {
        GridModel model = gridDataService.getGrid(gridId);
        if (Objects.nonNull(model)) {
            if (!model.getOpenId().equals(openId)) {
                throw new InternalException(ErrorBody.builder()
                        .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                        .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                        .detail("无法访问该网格").build(), new IllegalArgumentException(), HttpStatus.FORBIDDEN);
            }
            List<TradeLogModel> tradeLogModels = tradeLogDataService.getTradeLogByGridId(model.getOpenId(), model.getId());
            if (tradeLogModels.size() != 1) {
                log.error("grid has more than 1 trade log, openId: {}, gridId: {}, trade log size: {}", openId, gridId, tradeLogModels.size());
                throw new InternalException(ErrorBody.builder()
                        .code(CommonErrorCode.INTERNAL_DATA_ERROR.getErrorCode())
                        .reason(CommonErrorCode.INTERNAL_DATA_ERROR.getErrorMsg())
                        .detail("数据错误,暂无法编辑").build(), new RuntimeException("Internal Data Error"), HttpStatus.INTERNAL_SERVER_ERROR);
            }
            if (!Objects.equals(tradeLogModels.get(0).getTradeType(), TradeLogModel.TRADE_TYPE_BUY)) {
                log.error("grid trade log type is not buy, openId: {}, gridId: {}, trade log type: {}", openId, gridId, tradeLogModels.get(0).getTradeType());
                throw new InternalException(ErrorBody.builder()
                        .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                        .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                        .detail("数据错误,暂无法编辑").build(), new RuntimeException("Internal Data Error"), HttpStatus.INTERNAL_SERVER_ERROR);
            }


            Date buyAt = DateUtil.getZeroOfDay(new Date(request.getBuyAt()));
            Date updateAt = new Date();
            model.setHoldShares(request.getHoldShares());
            model.setBuyPrice(request.getBuyPrice());
            model.setBuyAt(buyAt);
            model.setUpdateAt(updateAt);

            gridDataService.updateGrid(model);
            tradeLogDataService.deleteByIdAndOpenId(tradeLogModels.get(0).getId(), openId);

            TradeLogModel tradeLog = TradeLogModel.builder()
                    .strategyId(model.getStrategyId())
                    .gridId(model.getId())
                    .gridType(model.getGridType())
                    .level(model.getLevel())
                    .tradeType(TradeLogModel.TRADE_TYPE_BUY)
                    .tradeShares(model.getHoldShares())
                    .tradePrice(model.getBuyPrice())
                    .theoreticalPrice(model.getTheoreticalBuyPrice())
                    .theoreticalShares(model.getTheoreticalBuyShares())
                    .tradeAt(model.getBuyAt())
                    .openId(openId)
                    .build();
            tradeLogDataService.create(tradeLog);

        } else {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("网格不存在").build(), new IllegalArgumentException(), HttpStatus.NOT_FOUND);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public void sellGrid(Long gridId, DeleteGridRequest request) {
        GridModel model = gridDataService.getGrid(gridId);
        if (Objects.nonNull(model)) {
            this.checkSellParams(request, model);
            int profit = (request.getSellPrice() - model.getBuyPrice()) * model.getHoldShares();
            Date tradeAt = DateUtil.getZeroOfDay(new Date(request.getSellAt()));

            TradeLogModel tradeLog = TradeLogModel.builder()
                    .strategyId(model.getStrategyId())
                    .gridId(model.getId())
                    .gridType(model.getGridType())
                    .level(model.getLevel())
                    .tradeType(TradeLogModel.TRADE_TYPE_SELL)
                    .tradeShares(request.getSellShares())
                    .tradePrice(request.getSellPrice())
                    .reminderShares(model.getHoldShares() - request.getSellShares())
                    .theoreticalPrice(model.getTheoreticalSellPrice())
                    .theoreticalShares(model.getTheoreticalSellShares())
                    .tradeAt(tradeAt)
                    .profit(profit)
                    .openId(model.getOpenId())
                    .build();
            tradeLogDataService.create(tradeLog);

            model.sell(request.getSellShares());
            gridDataService.sellUpdateGrid(model);

            strategyDataService.addRemainderShares(model.getOpenId(), model.getStrategyId(), model.getHoldShares());
        } else {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("网格不存在").build(), new IllegalArgumentException(), HttpStatus.NOT_FOUND);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public void deleteGrid(Long gridId, String openId) {
        GridModel grid = gridDataService.getGrid(gridId);
        List<GridModel> gridModels = gridDataService.getGrids(openId, grid.getStrategyId());
        int minLevel = 100;
        for (GridModel gridModel : gridModels) {
            if (gridModel.getGridType().equals(grid.getGridType())) {
                minLevel = Math.min(minLevel, gridModel.getLevel());
            }
        }
        if (minLevel != grid.getLevel()) {
            log.warn("delete grid failed, min level: {}, grid level: {}", minLevel, grid.getLevel());
            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "只能删除最低档位的网格",
                    new RuntimeException(), HttpStatus.BAD_REQUEST);
        }

        int gridCount = gridDataService.deleteById(gridId, openId);
        int logCount = tradeLogDataService.deleteByGridIdAndOpenId(gridId, openId);
        log.info("delete grid count: {}, delete tradeLog: {}", gridCount, logCount);
    }

    @Override
    public List<TradeLogDto> getGridTradeLogs(String openId, Long gridId) {
        List<TradeLogModel> models = tradeLogDataService.getTradeLogByGridId(openId, gridId);
        List<TradeLogDto> res = new ArrayList<>();
        for (TradeLogModel model : models) {
            TradeLogDto dto = new TradeLogDto();
            BeanUtils.copyProperties(model, dto);
            res.add(dto);
        }
        return res;
    }

    private void checkSellParams(DeleteGridRequest request, GridModel model) {
        if (!model.getOpenId().equals(UserContext.getOpenId())) {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("无法访问该网格").build(), new IllegalArgumentException(), HttpStatus.FORBIDDEN);
        }

        if (request.getSellShares() > model.getHoldShares()) {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("卖出股数超出本网格持有股数").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }

        if (request.getSellAt() < model.getBuyAt().getTime()) {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("卖出时间不能早于买入时间").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }
    }
}
