package com.chenminjie.sea.application.service.gtt.impl;

import com.chenminjie.sea.application.service.gtt.GttUserService;
import com.chenminjie.sea.common.dto.CreateGttUserRequest;
import com.chenminjie.sea.domain.core.gtt.GttUserDataService;
import com.chenminjie.sea.domain.model.gtt.GttUserModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GttUserServiceImpl implements GttUserService {
    @Autowired
    private GttUserDataService gttUserDataService;

    @Override
    public void createUser(CreateGttUserRequest createGttUserRequest) {
        GttUserModel model = new GttUserModel();
        BeanUtils.copyProperties(createGttUserRequest, model);
        gttUserDataService.createUser(model);
    }

    @Override
    public GttUserModel getUser(String appleId) {
        return gttUserDataService.getUser(appleId);
    }
}
