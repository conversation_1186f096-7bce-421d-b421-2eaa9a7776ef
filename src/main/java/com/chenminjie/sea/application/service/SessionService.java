package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.SessionDto;
import com.chenminjie.sea.domain.model.CreateSessionModel;
import com.chenminjie.sea.domain.model.SessionModel;

/**
 * @File: SessionService
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/17 23:40
 **/
public interface SessionService {

    SessionDto createSession(CreateSessionModel createSessionModel);

    SessionDto getSession(String sessionKey);

    SessionModel getSessionByOpenId(String openId);
}
