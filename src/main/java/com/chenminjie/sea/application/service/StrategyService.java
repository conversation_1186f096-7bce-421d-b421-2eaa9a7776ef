package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.FlexibleTradeLogDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.common.dto.*;
import me.chanjar.weixin.common.error.WxErrorException;

import java.util.List;

/**
 * @File: StrategyService.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/25 22:21
 * @update: 2025/03/02
 **/
public interface StrategyService {

    QueryStrategyResponse getPagedStrategies(String openId, int pageNum, int pageSize);

    Strategy createStrategy(CreateStrategyRequest request) throws WxErrorException;

    /**
     * Updating the strategy. There are 2 situations:
     * 1. If the strategy has valid grids, only "name", "code", and "maxFall" can be updated.
     * 2. If the strategy has no valid grids, all fields can be updated.
     * @param request
     * @param openId
     * @return
     */
    void updateStrategy(Long strategyId, UpdateStrategyRequest request, String openId);

    void deleteStrategy(Long id, String openId);

    Strategy getStrategy(String openId, Long strategyId);

    QueryPagedResponse<TradeLogDto> getStrategyTradeLogs(String openId, Long strategyId, int pageNum, int pageSize);

    StrategySummary getStrategySummary(String openId, Long strategyId);

    void tradeRetainShares(Long strategyId, TradeRetainSharesRequest request);

    List<FlexibleTradeLogDto> getFlexibleTradeLogs(Long strategyId);

    /**
     * 获取策略的统计信息列表
     * 
     * @param openId 用户openId
     * @param pageNumber 页码
     * @param pageSize 每页大小
     * @return 分页的策略统计信息
     */
    QueryPagedResponse<StrategyStatisticsResponse> getStrategyStatistics(String openId, Integer pageNumber, Integer pageSize);
}
