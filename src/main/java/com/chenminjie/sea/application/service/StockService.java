package com.chenminjie.sea.application.service;

import com.chenminjie.sea.common.dto.Stock;
import com.chenminjie.sea.common.dto.StockFilterRequest;
import com.chenminjie.sea.common.dto.StockIndexResponse;
import com.chenminjie.sea.common.dto.StockPriceResponse;
import com.chenminjie.sea.common.dto.StocksPriceResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

public interface StockService {
    void initFinancialData();

    List<Stock> searchByCode(String codePrefix);

    StockPriceResponse queryPrice(String code);

    StocksPriceResponse queryStocksPrice(Set<String> codes);

    void updateStockIndex(MultipartFile file, String metricType, Integer periodYear);

    StockIndexResponse queryStockIndex(Set<String> codes, String metricType);

    StockIndexResponse searchStockIndex(String codePrefix, String metricType);

    @Deprecated
    StockIndexResponse filterByPercentile(Double fiveYearPercentile, Double tenYearPercentile);

    StockIndexResponse filterStocks(StockFilterRequest request);
}
