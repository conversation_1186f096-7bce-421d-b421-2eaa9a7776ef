package com.chenminjie.sea.application.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaSecCheckService;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckRequest;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckResponse;
import com.chenminjie.sea.application.dto.FlexibleTradeLogDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.application.service.StrategyService;
import com.chenminjie.sea.common.dto.*;
import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.error.ErrorBody;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.common.util.DateUtil;
import com.chenminjie.sea.common.util.UserContext;
import com.chenminjie.sea.domain.core.grid.GridDataService;
import com.chenminjie.sea.domain.core.grid.TradeLogDataService;
import com.chenminjie.sea.domain.core.strategy.FlexibleTradeLogDataService;
import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
import com.chenminjie.sea.domain.model.FlexibleTradeLogModel;
import com.chenminjie.sea.domain.model.GridModel;
import com.chenminjie.sea.domain.model.StrategyModel;
import com.chenminjie.sea.domain.model.TradeLogModel;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.decampo.xirr.Transaction;
import org.decampo.xirr.Xirr;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @File: StrategyServiceImpl.java
 * @author: ChenMinJie
 * @create: 2023/04/25 22:24
 **/
@Slf4j
@Component
public class StrategyServiceImpl implements StrategyService {

    private final StrategyDataService strategyDataService;

    @Autowired
    private WxMaSecCheckService wxMaSecCheckService;

    @Autowired
    private GridDataService gridDataService;

    @Autowired
    private TradeLogDataService tradeLogDataService;

    @Autowired
    private FlexibleTradeLogDataService flexibleTradeLogDataService;

    @Autowired
    public StrategyServiceImpl(StrategyDataService strategyDataService) {
        this.strategyDataService = strategyDataService;
    }

    @Override
    public QueryStrategyResponse getPagedStrategies(String openId, int pageNum, int pageSize) {
        long total = PageHelper.count(() -> strategyDataService.getStrategyByOpenId(openId));
        PageHelper.startPage(pageNum, pageSize);
        List<StrategyModel> strategyModels = strategyDataService.getStrategyByOpenId(openId);
        QueryStrategyResponse response = QueryStrategyResponse.builder()
                .msg("ok")
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalPages((int) Math.ceil((double) total / pageSize))
                .result(new ArrayList<>()).build();
        strategyModels.forEach(x -> {
            Strategy strategy = new Strategy();
            BeanUtils.copyProperties(x, strategy);
            response.getResult().add(strategy);
        });
        return response;
    }

    @Override
    public Strategy createStrategy(CreateStrategyRequest request) throws WxErrorException {
        WxMaMsgSecCheckCheckResponse response = wxMaSecCheckService.checkMessage(WxMaMsgSecCheckCheckRequest.builder()
                .content(request.getName())
                .version("2")
                .scene(1)
                .openid(UserContext.getOpenId()).build());
        log.info("name({}) check resp: {}", request.getName(), response.getResult().getSuggest());
        if ("pass".equalsIgnoreCase(response.getResult().getSuggest())) {
            StrategyModel strategyModel = new StrategyModel();
            BeanUtils.copyProperties(request, strategyModel);
            strategyModel.setOpenId(UserContext.getOpenId());
            strategyModel.setBuyGrid(100);
            strategyModel.setBuyPrice(strategyModel.getTargetPrice());
            strategyModel.setUnionId("");
            strategyModel.setSellStrategy(1);

            strategyModel = strategyDataService.createStrategy(strategyModel);
            Strategy result = new Strategy();
            BeanUtils.copyProperties(strategyModel, result);
            return result;
        }
        throw new IllegalArgumentException("策略名称不合法");
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public void updateStrategy(Long strategyId, UpdateStrategyRequest request, String openId) {
        if (StringUtils.hasLength(request.getName())) {
            WxMaMsgSecCheckCheckResponse response;
            try {
                response = wxMaSecCheckService.checkMessage(WxMaMsgSecCheckCheckRequest.builder()
                        .content(request.getName())
                        .version("2")
                        .scene(1)
                        .openid(UserContext.getOpenId()).build());
                log.info("name({}) check resp: {}", request.getName(), response.getResult().getSuggest());
                if (!"pass".equalsIgnoreCase(response.getResult().getSuggest())) {
                    throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "策略名称不合法", new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
                }
            } catch (WxErrorException e) {
                throw new InternalException(CommonErrorCode.WX_SERVER_ERROR, "服务器繁忙,请稍后重试", new IllegalArgumentException(), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        int gridCount = gridDataService.getValidGridCount(openId, strategyId);
        StrategyModel strategyModel = strategyDataService.queryForUpdate(strategyId, openId);
        if (Objects.isNull(strategyModel)) {
            log.error("strategy not found, strategyId({}), openId({})", strategyId, openId);
            throw new InternalException(CommonErrorCode.ILLEGAL_ARGUMENTS, "策略不存在", new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }

        StrategyModel model = StrategyModel.builder()
                .id(strategyId)
                .openId(openId).build();
        if (gridCount == 0) {
            BeanUtils.copyProperties(request, model);
        } else if  (gridCount > 0) {
            if (StringUtils.hasLength(request.getCode())) {
                model.setCode(request.getCode());
            }
            if (StringUtils.hasLength(request.getName())) {
                model.setName(request.getName());
            }
            if (request.getMaxFall() != null) {
                model.setMaxFall(request.getMaxFall());
            }
        }

        strategyDataService.updateStrategy(model);
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public void deleteStrategy(Long id, String openId) {
        strategyDataService.deleteById(id, openId);
        gridDataService.deleteByStrategyId(id, openId);
    }

    @Override
    public Strategy getStrategy(String openId, Long strategyId) {
        StrategyModel model = strategyDataService.getStrategyById(strategyId, openId);
        if (Objects.isNull(model)) {
            log.error("strategy not found, strategyId({}), openId({})", strategyId, openId);
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("策略不存在").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }
        Strategy strategy = new Strategy();
        BeanUtils.copyProperties(model, strategy);
        return strategy;
    }

    @Override
    public QueryPagedResponse<TradeLogDto> getStrategyTradeLogs(String openId, Long strategyId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<TradeLogModel> models = tradeLogDataService.getTradeLogByStrategyId(openId, strategyId);
        QueryPagedResponse<TradeLogDto> response = new QueryPagedResponse<>();
        response.setMsg("ok");
        response.setPageNum(pageNum);
        response.setPageSize(pageSize);
        response.setResult(new ArrayList<>());
        if (pageNum == 1) {
            long total = PageHelper.count(() -> tradeLogDataService.getTradeLogByStrategyId(openId, strategyId));
            response.setTotalPages((int) Math.ceil((double) total / pageSize));
        }

        if (models.isEmpty()) {
            return response;
        }

        Set<Long> gridIds = new HashSet<>();
        for (TradeLogModel model : models) {
            TradeLogDto dto = new TradeLogDto();
            BeanUtils.copyProperties(model, dto);
            response.getResult().add(dto);
            gridIds.add(model.getGridId());
        }

        PageHelper.clearPage();
        List<GridModel> gridModels = gridDataService.getGrids(openId, gridIds);
        Map<Long, GridModel> gridModelMap = gridModels.stream().collect(Collectors.toMap(GridModel::getId, gridModel -> gridModel));

        for (TradeLogDto dto : response.getResult()) {
            if (dto.getTradeType().equals(TradeLogModel.TRADE_TYPE_SELL)) {
                GridModel model = gridModelMap.get(dto.getGridId());
                int buyShares = dto.getTradeShares() + model.getHoldShares();
                dto.setReminderShares(model.getHoldShares());
                dto.setProfit((dto.getTradePrice() - model.getBuyPrice()) * buyShares);
                if (buyShares == 0) {
                    dto.setProfitRatio(0);
                } else {
                    double ratio = dto.getProfit() * 1.0 / (model.getBuyPrice() * buyShares) * 10000;
                    dto.setProfitRatio((int) ratio);
                }
                dto.setHoldTime(DateUtil.getAbsDaysBetween(model.getBuyAt(), dto.getTradeAt()));

                double apyDouble = dto.getProfit() * 1.0 / (buyShares * model.getBuyPrice()) / dto.getHoldTime() * 365 *10000;
                dto.setApy((int) apyDouble);
            }
        }

        return response;
    }

    @Override
    public StrategySummary getStrategySummary(String openId, Long strategyId) {
        List<TradeLogModel> models = tradeLogDataService.getTradeLogByStrategyId(openId, strategyId);
        List<FlexibleTradeLogModel> flexibleTradeLogModels = flexibleTradeLogDataService.getLogsByStrategyIdAndOpenId(strategyId, openId);
        StrategySummary summary = new StrategySummary(0D, 0);

        if (models.isEmpty()) {
            return summary;
        }

        Map<Long, List<TradeLogModel>> tradeLogMap = models.stream().collect(Collectors.groupingBy(TradeLogModel::getGridId));

        int totalProfit = getTotalProfit(tradeLogMap, flexibleTradeLogModels);
        double xirr = getXirr(tradeLogMap, flexibleTradeLogModels);

        summary.setTotalProfit(totalProfit);
        summary.setXirr(xirr);
        return summary;
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 5, rollbackFor = Throwable.class)
    @Override
    public void tradeRetainShares(Long strategyId, TradeRetainSharesRequest request) {
        StrategyModel strategyModel = strategyDataService.queryForUpdate(strategyId, UserContext.getOpenId());
        if (request.getTradeType().equals(FlexibleTradeLogModel.TRADE_TYPE_SELL)) {
            if (strategyModel.getRemainderShares().equals(request.getTradeShares())) {
                strategyDataService.addRemainderShares(UserContext.getOpenId(), strategyId, -request.getTradeShares());
                FlexibleTradeLogModel model = FlexibleTradeLogModel.builder()
                        .strategyId(strategyId)
                        .tradeShares(request.getTradeShares())
                        .tradeType(request.getTradeType())
                        .tradePrice(request.getTradePrice())
                        .tradeAt(DateUtil.getZeroOfDay(new Date(request.getTradeAt())))
                        .openId(UserContext.getOpenId())
                        .build();
                flexibleTradeLogDataService.createFlexibleTradeLog(model);
                return;
            }
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("暂只支持全部卖出").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        } else {
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("该操作暂未支持").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public List<FlexibleTradeLogDto> getFlexibleTradeLogs(Long strategyId) {
        List<FlexibleTradeLogModel> models = flexibleTradeLogDataService.getLogsByStrategyIdAndOpenId(strategyId, UserContext.getOpenId());
        List<FlexibleTradeLogDto> res = new ArrayList<>();
        for (FlexibleTradeLogModel model : models) {
            FlexibleTradeLogDto dto = new FlexibleTradeLogDto();
            BeanUtils.copyProperties(model, dto);
            res.add(dto);
        }
        return res;
    }

    private int getTotalProfit(Map<Long, List<TradeLogModel>> tradeLogMap, List<FlexibleTradeLogModel> flexibleTradeLogModels) {
        int totalProfit = 0;
        if (flexibleTradeLogModels.isEmpty()) {
            for (Map.Entry<Long, List<TradeLogModel>> entry : tradeLogMap.entrySet()) {
                if (entry.getValue().size() == 2) {
                    TradeLogModel first = entry.getValue().get(0);
                    TradeLogModel second = entry.getValue().get(1);

                    if (first.getTradeType().equals(TradeLogModel.TRADE_TYPE_SELL)) {
                        totalProfit += (first.getTradePrice() - second.getTradePrice()) * second.getTradeShares();
                    } else {
                        totalProfit += (second.getTradePrice() - first.getTradePrice()) * first.getTradeShares();
                    }
                }
            }
        } else {
            for (Map.Entry<Long, List<TradeLogModel>> entry : tradeLogMap.entrySet()) {
                if (entry.getValue().size() == 2) {
                    TradeLogModel first = entry.getValue().get(0);
                    TradeLogModel second = entry.getValue().get(1);

                    if (first.getTradeType().equals(TradeLogModel.TRADE_TYPE_SELL)) {
                        totalProfit += first.getTradePrice() * first.getTradeShares() - second.getTradePrice() * second.getTradeShares();
                    } else {
                        totalProfit += second.getTradePrice() * second.getTradeShares() - first.getTradePrice() * first.getTradeShares();
                    }
                }
            }
            for (FlexibleTradeLogModel tradeLogModel : flexibleTradeLogModels) {
                if (tradeLogModel.getTradeType().equals(FlexibleTradeLogModel.TRADE_TYPE_SELL)) {
                    totalProfit += tradeLogModel.getTradePrice() * tradeLogModel.getTradeShares();
                }
            }
        }
        return totalProfit;
    }

    private double getXirr(Map<Long, List<TradeLogModel>> tradeLogMap, List<FlexibleTradeLogModel> flexibleTradeLogModels) {
        List<Transaction> transactions = new ArrayList<>();
        // 未卖出预留利润
        if (flexibleTradeLogModels.isEmpty()) {
            for (Map.Entry<Long, List<TradeLogModel>> entry : tradeLogMap.entrySet()) {
                if (entry.getValue().size() == 2) {
                    TradeLogModel first = entry.getValue().get(0);
                    TradeLogModel second = entry.getValue().get(1);

                    if (first.getTradeType().equals(TradeLogModel.TRADE_TYPE_SELL)) {
                        Date sellDate = first.getTradeAt();
                        if (DateUtil.isSameDay(first.getTradeAt(), second.getTradeAt())) {
                            sellDate = new Date(sellDate.getTime() + 24 * 60 * 60 * 1000);
                        }
                        if (!second.getTradeShares().equals(0)) {
                            transactions.add(new Transaction(first.getTradePrice() * second.getTradeShares(), sellDate));
                            transactions.add(new Transaction(-second.getTradePrice() * second.getTradeShares(), second.getTradeAt()));
                        }
                    } else {
                        Date sellDate = second.getTradeAt();
                        if (DateUtil.isSameDay(first.getTradeAt(), second.getTradeAt())) {
                            sellDate = new Date(sellDate.getTime() + 24 * 60 * 60 * 1000);
                        }
                        if (!first.getTradeShares().equals(0)) {
                            transactions.add(new Transaction(second.getTradePrice() * first.getTradeShares(), sellDate));
                            transactions.add(new Transaction(-first.getTradePrice() * first.getTradeShares(), first.getTradeAt()));
                        }
                    }
                }
            }
        } else {
            for (Map.Entry<Long, List<TradeLogModel>> entry : tradeLogMap.entrySet()) {
                if (entry.getValue().size() == 2) {
                    TradeLogModel first = entry.getValue().get(0);
                    TradeLogModel second = entry.getValue().get(1);

                    if (first.getTradeType().equals(TradeLogModel.TRADE_TYPE_SELL)) {
                        Date sellDate = first.getTradeAt();
                        if (DateUtil.isSameDay(first.getTradeAt(), second.getTradeAt())) {
                            sellDate = new Date(sellDate.getTime() + 24 * 60 * 60 * 1000);
                        }
                        if (!second.getTradeShares().equals(0)) {
                            transactions.add(new Transaction(first.getTradePrice() * first.getTradeShares(), sellDate));
                            transactions.add(new Transaction(-second.getTradePrice() * second.getTradeShares(), second.getTradeAt()));
                        }
                    } else {
                        Date sellDate = second.getTradeAt();
                        if (DateUtil.isSameDay(first.getTradeAt(), second.getTradeAt())) {
                            sellDate = new Date(sellDate.getTime() + 24 * 60 * 60 * 1000);
                        }
                        if (!first.getTradeShares().equals(0)) {
                            transactions.add(new Transaction(second.getTradePrice() * second.getTradeShares(), sellDate));
                            transactions.add(new Transaction(-first.getTradePrice() * first.getTradeShares(), first.getTradeAt()));
                        }
                    }
                }
            }
            for (FlexibleTradeLogModel tradeLogModel : flexibleTradeLogModels) {
                if (tradeLogModel.getTradeType().equals(FlexibleTradeLogModel.TRADE_TYPE_SELL)) {
                    transactions.add((new Transaction(tradeLogModel.getTradePrice() * tradeLogModel.getTradeShares(), tradeLogModel.getTradeAt())));
                }
            }
        }

        if (transactions.isEmpty()) {
            return 0;
        }
        return new Xirr(transactions).xirr();
    }

    @Override
    public QueryPagedResponse<StrategyStatisticsResponse> getStrategyStatistics(String openId, Integer pageNumber, Integer pageSize) {
        if (pageNumber <= 0 || pageSize <= 0 || pageSize > 100) {
            return QueryPagedResponse.<StrategyStatisticsResponse>builder()
                    .msg("error")
                    .pageNum(pageNumber)
                    .pageSize(pageSize)
                    .totalPages(0)
                    .result(Collections.emptyList())
                    .build();
        }

        // 1. 分页获取策略列表
        long total = PageHelper.count(() -> strategyDataService.getStrategyByOpenId(openId));
        PageHelper.startPage(pageNumber, pageSize);
        List<StrategyModel> strategies = strategyDataService.getStrategyByOpenId(openId);

        if (strategies.isEmpty()) {
            return QueryPagedResponse.<StrategyStatisticsResponse>builder()
                    .msg("ok")
                    .pageNum(pageNumber)
                    .pageSize(pageSize)
                    .totalPages(0)
                    .result(Collections.emptyList())
                    .build();
        }

        List<Long> strategyIds = strategies.stream()
                .map(StrategyModel::getId)
                .collect(Collectors.toList());

        // 2. 并行查询各项统计数据
        CompletableFuture<Map<Long, Integer>> investmentsFuture = CompletableFuture.supplyAsync(() ->
            strategyDataService.getTotalInvestments(strategyIds));
        
        CompletableFuture<Map<Long, Integer>> sellCountsFuture = CompletableFuture.supplyAsync(() ->
            strategyDataService.getGridSellCounts(strategyIds));
        
        CompletableFuture<Map<Long, Integer>> profitsFuture = CompletableFuture.supplyAsync(() ->
            strategyDataService.getTotalProfits(strategyIds));
        
        CompletableFuture<Map<Long, Integer>> holdingSharesFuture = CompletableFuture.supplyAsync(() ->
            strategyDataService.getTotalHoldingShares(strategyIds));
        
        CompletableFuture<Map<Long, Integer>> retainSharesFuture = CompletableFuture.supplyAsync(() ->
            strategyDataService.getRetainProfitShares(strategyIds));

        // 3. 等待所有查询完成并组装结果
        try {
            CompletableFuture.allOf(
                investmentsFuture,
                sellCountsFuture,
                profitsFuture,
                holdingSharesFuture,
                retainSharesFuture
            ).join();

            Map<Long, Integer> investments = investmentsFuture.join();
            Map<Long, Integer> sellCounts = sellCountsFuture.join();
            Map<Long, Integer> profits = profitsFuture.join();
            Map<Long, Integer> holdingShares = holdingSharesFuture.join();
            Map<Long, Integer> retainShares = retainSharesFuture.join();

            List<StrategyStatisticsResponse> statistics = strategies.stream()
                .map(strategy -> new StrategyStatisticsResponse(
                    strategy.getId(),
                    investments.getOrDefault(strategy.getId(), 0),
                    sellCounts.getOrDefault(strategy.getId(), 0),
                    profits.getOrDefault(strategy.getId(), 0),
                    holdingShares.getOrDefault(strategy.getId(), 0),
                    retainShares.getOrDefault(strategy.getId(), 0)
                ))
                .collect(Collectors.toList());

            return QueryPagedResponse.<StrategyStatisticsResponse>builder()
                    .msg("ok")
                    .pageNum(pageNumber)
                    .pageSize(pageSize)
                    .totalPages((int) Math.ceil((double) total / pageSize))
                    .result(statistics)
                    .build();

        } catch (Exception e) {
            log.error("Failed to get strategy statistics for strategies: {}", strategyIds, e);
            throw new InternalException(
                ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("获取策略统计信息失败")
                    .build(),
                e,
                HttpStatus.BAD_REQUEST
            );
        } finally {
            PageHelper.clearPage();
        }
    }
}
