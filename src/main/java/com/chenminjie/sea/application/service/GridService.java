package com.chenminjie.sea.application.service;

import com.chenminjie.sea.application.dto.GridDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.common.dto.CreateGridRequest;
import com.chenminjie.sea.common.dto.DeleteGridRequest;
import com.chenminjie.sea.common.dto.UpdateGridRequest;

import java.util.List;

/**
 * @File: GridService
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/12 16:35
 **/
public interface GridService {

    GridDto buyGrid(CreateGridRequest request);

    List<GridDto> getAllGrids(String openId, Long strategyId);

    List<GridDto> getAllGridsWithInvalid(String openId, Long strategyId);

    GridDto getGrid(String openId, Long gridId);

    void updateGrid(String openId, Long gridId, UpdateGridRequest request);

    void sellGrid(Long gridId, DeleteGridRequest request);

    void deleteGrid(Long gridId, String openId);

    List<TradeLogDto> getGridTradeLogs(String openId, Long gridId);
}
