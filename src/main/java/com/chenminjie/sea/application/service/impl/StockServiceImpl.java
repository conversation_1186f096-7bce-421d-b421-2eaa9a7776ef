package com.chenminjie.sea.application.service.impl;

import com.chenminjie.sea.application.service.StockService;
import com.chenminjie.sea.common.dto.Stock;
import com.chenminjie.sea.common.dto.StockFilterRequest;
import com.chenminjie.sea.common.dto.StockIndexResponse;
import com.chenminjie.sea.common.dto.StockPriceResponse;
import com.chenminjie.sea.common.dto.StocksPriceResponse;
import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.domain.core.financial.FinancialDataService;
import com.chenminjie.sea.domain.core.financial.FinancialService;
import com.chenminjie.sea.domain.core.financial.dto.*;
import com.chenminjie.sea.domain.model.StockIndexModel;
import com.chenminjie.sea.domain.model.StockModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StockServiceImpl implements StockService {

    @Autowired
    private FinancialService financialService;

    @Autowired
    private FinancialDataService financialDataService;

    /**
     * 辅助类：用于CSV列名到索引的映射
     */
    private static class CsvHeaderMapping {
        private final Map<String, Integer> columnMapping = new HashMap<>();
        private final Map<String, String> normalizedNameMap = new HashMap<>();
        
        public CsvHeaderMapping(String headerLine) {
            // 处理可能存在的BOM字符
            if (headerLine.startsWith("\uFEFF")) {
                headerLine = headerLine.substring(1);
                log.info("检测到并移除了BOM字符");
            }
            
            String[] headers = headerLine.split(",");
            for (int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].trim();
                columnMapping.put(headers[i], i);
                // 存储标准化的名称映射关系
                String normalizedName = normalizeColumnName(headers[i]);
                normalizedNameMap.put(normalizedName, headers[i]);
                log.debug("CSV表头映射 - 原始列名: [{}], 标准化列名: [{}], 索引: {}", headers[i], normalizedName, i);
            }
        }
        
        // 标准化列名（去除BOM和其他特殊字符）
        private String normalizeColumnName(String columnName) {
            // 移除BOM和其他不可见字符
            return columnName.replaceAll("[\\p{C}]", "");
        }
        
        public Integer getColumnIndex(String columnName) {
            // 首先尝试直接匹配
            Integer index = columnMapping.get(columnName);
            if (index != null) {
                return index;
            }
            
            // 如果直接匹配失败，尝试标准化后匹配
            String normalizedName = normalizeColumnName(columnName);
            String actualColumnName = normalizedNameMap.get(normalizedName);
            if (actualColumnName != null) {
                index = columnMapping.get(actualColumnName);
                log.info("通过标准化名称找到列映射 - 请求列名: [{}], 标准化名称: [{}], 实际列名: [{}], 索引: {}", 
                        columnName, normalizedName, actualColumnName, index);
                return index;
            }
            
            log.warn("未找到列名: [{}] 的映射，可用的列名: {}", columnName, columnMapping.keySet());
            return null;
        }
        
        public String getValue(String[] values, String columnName) {
            Integer index = getColumnIndex(columnName);
            if (index == null) {
                return null;
            }
            if (index >= values.length) {
                log.warn("列索引: {} 超出了值数组长度: {}", index, values.length);
                return null;
            }
            String value = values[index];
            log.debug("获取列 [{}] 的值: [{}]", columnName, value);
            return value;
        }
        
        @Override
        public String toString() {
            return columnMapping.toString();
        }
    }

    @Override
    public void initFinancialData() {
        QueryStocksResponse stocksResponse = financialService.queryStocks(new QueryRequest());
        QueryFundsResponse fundsResponse = financialService.queryFunds(new QueryRequest());
        QueryIndexsResponse indexsResponse = financialService.queryIndexs(new QueryRequest());
        List<StockModel> stockModels = new ArrayList<>(stocksResponse.getData().size() + fundsResponse.getData().size());
        for (List<String> stock : stocksResponse.getData()) {
            StockModel stockModel = StockModel.builder()
                    .code(stock.get(0).substring(2))
                    .name(stock.get(1))
                    .market(stock.get(0).substring(0, 2))
                    .type(0).build();
            stockModels.add(stockModel);
        }

        for (List<String> fund : fundsResponse.getData()) {
            StockModel stockModel = StockModel.builder()
                    .code(fund.get(0))
                    .name(fund.get(2))
                    .type(1).build();
            stockModels.add(stockModel);
        }

        for (List<String> index : indexsResponse.getData()) {
            StockModel stockModel = StockModel.builder()
                    .code(index.get(0))
                    .name(index.get(1))
                    .type(2).build();
            stockModels.add(stockModel);
        }

        log.info("size: {}", stockModels.size());

        int batchSize = 1000;
        for (int i = 0; i < stockModels.size(); i += batchSize) {
            int end = Math.min(i + batchSize, stockModels.size());
            int count = financialDataService.batchUpsert(stockModels.subList(i, end));
            log.info("batch insert start: {}, end: {}, count: {}", i, end, count);
        }
    }

    @Override
    public List<Stock> searchByCode(String codePrefix) {
        List<StockModel> stockModels = financialDataService.searchByCode(codePrefix);
        return stockModels.stream().map(Stock::from).collect(Collectors.toList());
    }

    @Override
    public StockPriceResponse queryPrice(String code) {
        QueryPriceResponse queryPriceResponse = financialService.queryPrice(code);
        if (queryPriceResponse.getCode().equals(200)) {
            StockPriceResponse response = new StockPriceResponse();
            BeanUtils.copyProperties(queryPriceResponse.getData(), response);
            return response;
        }
        log.error("query price failed, code: {}, res code: {}, msg: {}", code, queryPriceResponse.getCode(),queryPriceResponse.getMessage());
        throw new InternalException(CommonErrorCode.FINANCIAL_SERVER_ERROR, "服务器繁忙,请稍侯重试", new RuntimeException(), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Override
    public StocksPriceResponse queryStocksPrice(Set<String> codes) {
        Set<QueryPriceResponse> responses = financialService.queryStocksPrice(codes);
        StocksPriceResponse response = new StocksPriceResponse();
        List<StockModel> stockModels = financialDataService.queryStocksByCodes(new ArrayList<>(codes));
        Map<String, StockModel> stockModelMap = stockModels.stream().collect(Collectors.toMap(StockModel::getCode, x -> x));
        for (QueryPriceResponse res : responses) {
            if (res.getCode().equals(200)) {
                StockPriceResponse stockPriceResponse = new StockPriceResponse();
                BeanUtils.copyProperties(res.getData(), stockPriceResponse);
                if (stockModelMap.containsKey(res.getData().getCode())) {
                    StockModel stockModel = stockModelMap.get(res.getData().getCode());
                    if (StringUtils.isNotBlank(stockModel.getMaxValue()) && StringUtils.isNotBlank(stockModel.getMinValue())) {
                        stockPriceResponse.setMaxValue(Double.parseDouble(stockModel.getMaxValue()));
                        stockPriceResponse.setMinValue(Double.parseDouble(stockModel.getMinValue()));
                    }
                }
                response.getSuccess().add(stockPriceResponse);
            }
        }

        if (codes.size() != response.getSuccess().size()) {
            Set<String> queried = response.getSuccess().stream().map(StockPriceResponse::getCode).collect(Collectors.toSet());
            Set<String> failed = new HashSet<>(codes);
            failed.removeAll(queried);
            response.getFailed().addAll(failed);
        }
        return response;
    }

    @Override
    public void updateStockIndex(MultipartFile file, String metricType, Integer periodYear) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            // 读取表头
            String headerLine = reader.readLine();
            if (headerLine == null) {
                throw new RuntimeException("CSV file is empty");
            }
            
            log.info("CSV表头: {}", headerLine);
            CsvHeaderMapping headerMapping = new CsvHeaderMapping(headerLine);
            
            // 记录表头中的列索引信息
            log.info("CSV表头映射: {}", headerMapping.columnMapping);
            
            String line;
            List<StockIndexModel> indexModels = new ArrayList<>();
            int lineNumber = 1; // 从1开始计数，因为表头是第1行
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                log.info("处理第{}行数据: {}", lineNumber, line);
                StockIndexModel indexModel = parseCsvLine(line, headerMapping);
                if (Objects.nonNull(indexModel)) {
                    if (indexModel.getCode() == null) {
                        log.warn("第{}行解析后code为null, 原始行数据: {}", lineNumber, line);
                    } else {
                        indexModel.setMetricType(metricType);
                        indexModel.setPeriodYear(periodYear);
                        indexModels.add(indexModel);
                    }
                } else {
                    log.warn("第{}行解析结果为null, 原始行数据: {}", lineNumber, line);
                }
            }
            log.info("CSV文件共解析{}行数据，有效数据{}条", lineNumber-1, indexModels.size());
            financialDataService.batchUpsertIndex(indexModels);
        } catch (IOException e) {
            log.error("处理CSV文件失败", e);
            throw new RuntimeException("Failed to process CSV file", e);
        }
    }

    @Override
    public StockIndexResponse queryStockIndex(Set<String> codes, String metricType) {
        List<StockIndexModel> indexModels5Y = financialDataService.getIndexByCodes(new ArrayList<>(codes), metricType, 5);
        List<StockIndexModel> indexModels10Y = financialDataService.getIndexByCodes(new ArrayList<>(codes), metricType, 10);
        StockIndexResponse response = new StockIndexResponse();
        
        Map<String, StockIndexResponse.StockIndex> mergedData = new HashMap<>();
        
        // 处理5年数据
        for (StockIndexModel model5Y : indexModels5Y) {
            StockIndexResponse.StockIndex stockIndex = new StockIndexResponse.StockIndex();
            BeanUtils.copyProperties(model5Y, stockIndex);
            stockIndex.setPeTtm5Y(model5Y.getPeTtm());
            stockIndex.setPeTtmPercentile5Y(model5Y.getPeTtmPercentile());
            stockIndex.setPb5Y(model5Y.getPb());
            stockIndex.setPbPercentile5Y(model5Y.getPbPercentile());
            stockIndex.setPsTtm5Y(model5Y.getPsTtm());
            stockIndex.setPsTtmPercentile5Y(model5Y.getPsTtmPercentile());
            stockIndex.setUpdateTimestamp(model5Y.getUpdateTime().getTime());
            mergedData.put(model5Y.getCode(), stockIndex);
        }
        
        // 处理10年数据并合并
        for (StockIndexModel model10Y : indexModels10Y) {
            StockIndexResponse.StockIndex stockIndex = mergedData.get(model10Y.getCode());
            if (stockIndex != null) {
                stockIndex.setPeTtm10Y(model10Y.getPeTtm());
                stockIndex.setPeTtmPercentile10Y(model10Y.getPeTtmPercentile());
                stockIndex.setPb10Y(model10Y.getPb());
                stockIndex.setPbPercentile10Y(model10Y.getPbPercentile());
                stockIndex.setPsTtm10Y(model10Y.getPsTtm());
                stockIndex.setPsTtmPercentile10Y(model10Y.getPsTtmPercentile());
            }
        }
        
        // 将合并后的数据添加到响应对象中，并按照代码排序
        response.getSuccess().addAll(
            mergedData.values().stream()
                .sorted(Comparator.comparing(StockIndexResponse.StockIndex::getCode))
                .collect(Collectors.toList())
        );
        
        // 处理失败的代码
        if (codes.size() != response.getSuccess().size()) {
            Set<String> queried = response.getSuccess().stream()
                    .map(StockIndexResponse.StockIndex::getCode)
                    .collect(Collectors.toSet());
            Set<String> failed = new HashSet<>(codes);
            failed.removeAll(queried);
            response.getFailed().addAll(failed);
        }
        
        return response;
    }

    @Override
    public StockIndexResponse searchStockIndex(String codePrefix, String metricType) {
        List<StockIndexModel> indexModels5Y = financialDataService.searchStockIndex(codePrefix, metricType, 5);
        List<StockIndexModel> indexModels10Y = financialDataService.searchStockIndex(codePrefix, metricType, 10);
        
        StockIndexResponse response = new StockIndexResponse();
        Map<String, StockIndexResponse.StockIndex> mergedData = new HashMap<>();
        
        // 处理5年数据
        for (StockIndexModel model5Y : indexModels5Y) {
            StockIndexResponse.StockIndex stockIndex = new StockIndexResponse.StockIndex();
            BeanUtils.copyProperties(model5Y, stockIndex);
            stockIndex.setPeTtm5Y(model5Y.getPeTtm());
            stockIndex.setPeTtmPercentile5Y(model5Y.getPeTtmPercentile());
            stockIndex.setPb5Y(model5Y.getPb());
            stockIndex.setPbPercentile5Y(model5Y.getPbPercentile());
            stockIndex.setPsTtm5Y(model5Y.getPsTtm());
            stockIndex.setPsTtmPercentile5Y(model5Y.getPsTtmPercentile());
            stockIndex.setUpdateTimestamp(model5Y.getUpdateTime().getTime());
            mergedData.put(model5Y.getCode(), stockIndex);
        }
        
        // 处理10年数据并合并
        for (StockIndexModel model10Y : indexModels10Y) {
            StockIndexResponse.StockIndex stockIndex = mergedData.get(model10Y.getCode());
            if (stockIndex != null) {
                stockIndex.setPeTtm10Y(model10Y.getPeTtm());
                stockIndex.setPeTtmPercentile10Y(model10Y.getPeTtmPercentile());
                stockIndex.setPb10Y(model10Y.getPb());
                stockIndex.setPbPercentile10Y(model10Y.getPbPercentile());
                stockIndex.setPsTtm10Y(model10Y.getPsTtm());
                stockIndex.setPsTtmPercentile10Y(model10Y.getPsTtmPercentile());
            }
        }
        
        // 将合并后的数据添加到响应对象中
        response.getSuccess().addAll(
            mergedData.values().stream()
                .sorted(Comparator.comparing(StockIndexResponse.StockIndex::getCode))
                .collect(Collectors.toList())
        );
        
        return response;
    }

    @Override
    @Deprecated
    public StockIndexResponse filterByPercentile(Double fiveYearPercentile, Double tenYearPercentile) {
        StockFilterRequest request = new StockFilterRequest();
        request.setPePercentile5Y(fiveYearPercentile);
        request.setPePercentile10Y(tenYearPercentile);
        request.setWeightingMethods(new HashSet<>(Arrays.asList("ewpvo", "mcw")));
        return filterStocks(request);
    }

    @Override
    public StockIndexResponse filterStocks(StockFilterRequest request) {
        StockIndexResponse response = new StockIndexResponse();

        // Process each requested weighting method
        for (String metricType : request.getWeightingMethods()) {
            List<StockIndexModel> models = financialDataService.filterByMultiplePercentiles(
                metricType,
                request.getPePercentile5Y(),
                request.getPePercentile10Y(),
                request.getPbPercentile5Y(),
                request.getPbPercentile10Y()
            );

            // Get codes for this metric type
            Set<String> codesForMetricType = models.stream()
                    .map(StockIndexModel::getCode)
                    .collect(Collectors.toSet());

            // Process models directly to response list
            processModels(models, codesForMetricType, response.getSuccess(), metricType);
        }

        // Sort the results by code
        response.setSuccess(
            response.getSuccess().stream()
                .sorted(Comparator.comparing(StockIndexResponse.StockIndex::getCode))
                .collect(Collectors.toList())
        );

        return response;
    }

    private void processModels(List<StockIndexModel> models, Set<String> commonCodes,
                              Map<String, StockIndexResponse.StockIndex> mergedData,
                              String metricType) {
        // Group models by code to handle 5Y and 10Y data together
        Map<String, List<StockIndexModel>> modelsByCode = models.stream()
            .filter(model -> commonCodes.contains(model.getCode()))
            .collect(Collectors.groupingBy(StockIndexModel::getCode));

        modelsByCode.forEach((code, codeModels) -> {
            StockIndexResponse.StockIndex stockIndex = mergedData.computeIfAbsent(code, k -> {
                StockIndexModel firstModel = codeModels.get(0);
                StockIndexResponse.StockIndex newIndex = new StockIndexResponse.StockIndex();
                newIndex.setCode(code);
                newIndex.setName(firstModel.getName());
                newIndex.setMetricType(metricType);
                newIndex.setPublishDate(firstModel.getPublishDate());
                newIndex.setUpdateTimestamp(firstModel.getUpdateTime().getTime());
                return newIndex;
            });

            // Sort models to ensure 5Y data is processed before 10Y data
            codeModels.sort(Comparator.comparing(StockIndexModel::getPeriodYear));
            // Process all models for this code (both 5Y and 10Y)
            codeModels.forEach(model -> setModelData(model, stockIndex));
        });
    }

    private void processModels(List<StockIndexModel> models, Set<String> commonCodes,
                              List<StockIndexResponse.StockIndex> resultList,
                              String metricType) {
        // Group models by code to handle 5Y and 10Y data together
        Map<String, List<StockIndexModel>> modelsByCode = models.stream()
            .filter(model -> commonCodes.contains(model.getCode()))
            .collect(Collectors.groupingBy(StockIndexModel::getCode));

        modelsByCode.forEach((code, codeModels) -> {
            // Sort models to ensure 5Y data is processed before 10Y data
            codeModels.sort(Comparator.comparing(StockIndexModel::getPeriodYear));
            StockIndexModel firstModel = codeModels.get(0);
            StockIndexResponse.StockIndex stockIndex = new StockIndexResponse.StockIndex();
            stockIndex.setCode(code);
            stockIndex.setName(firstModel.getName());
            stockIndex.setMetricType(metricType);
            stockIndex.setPublishDate(firstModel.getPublishDate());
            stockIndex.setUpdateTimestamp(firstModel.getUpdateTime().getTime());

            // Process all models for this code (both 5Y and 10Y)
            codeModels.forEach(model -> setModelData(model, stockIndex));
            resultList.add(stockIndex);
        });
    }

    private void setModelData(StockIndexModel model, StockIndexResponse.StockIndex stockIndex) {
        if (model.getPeriodYear() == 5) {
            stockIndex.setPeTtm5Y(model.getPeTtm());
            stockIndex.setPeTtmPercentile5Y(model.getPeTtmPercentile());
            stockIndex.setPb5Y(model.getPb());
            stockIndex.setPbPercentile5Y(model.getPbPercentile());
            stockIndex.setPsTtm5Y(model.getPsTtm());
            stockIndex.setPsTtmPercentile5Y(model.getPsTtmPercentile());
            stockIndex.setChangeRate(model.getChangeRate());
            stockIndex.setClosePoint(model.getClosePoint());
            stockIndex.setDividendYield5Y(model.getDividendYield());
        } else if (model.getPeriodYear() == 10) {
            stockIndex.setPeTtm10Y(model.getPeTtm());
            stockIndex.setPeTtmPercentile10Y(model.getPeTtmPercentile());
            stockIndex.setPb10Y(model.getPb());
            stockIndex.setPbPercentile10Y(model.getPbPercentile());
            stockIndex.setPsTtm10Y(model.getPsTtm());
            stockIndex.setPsTtmPercentile10Y(model.getPsTtmPercentile());
            // Only set these fields if they haven't been set by 5Y data
            if (stockIndex.getChangeRate() == null) {
                stockIndex.setChangeRate(model.getChangeRate());
            }
            if (stockIndex.getClosePoint() == null) {
                stockIndex.setClosePoint(model.getClosePoint());
            }
            stockIndex.setDividendYield10Y(model.getDividendYield());
        }
    }

    /**
     * 使用列名映射解析CSV行数据
     */
    private StockIndexModel parseCsvLine(String line, CsvHeaderMapping headerMapping) {
        if (line == null || line.trim().isEmpty()) {
            log.warn("解析到空行或只包含空白字符的行");
            return null;
        }
        
        String[] values = line.split(",", -1); // 保留所有空字段
        for (int i = 0; i < values.length; i++) {
            values[i] = values[i].trim();
        }
        
        StockIndexModel indexModel = new StockIndexModel();
        
        // 记录原始的指数代码值及处理过程
        Integer codeColumnIndex = headerMapping.getColumnIndex("指数代码");
        String rawCodeValue = codeColumnIndex != null && codeColumnIndex < values.length ? values[codeColumnIndex] : null;
        String codeValue = headerMapping.getValue(values, "指数代码");
        
        log.info("解析指数代码 - 列索引: {}, 原始值: {}, 处理后值: {}", codeColumnIndex, rawCodeValue, codeValue);
        
        if (codeValue != null) {
            // 去除可能的引号和等号
            String cleanedCode = codeValue.replaceAll("=\\s*[\"\']?|[\"\']", "");
            // 检查代码是否只包含字母和数字
            if (cleanedCode.matches("^[a-zA-Z0-9]+$")) {
                indexModel.setCode(cleanedCode);
                log.info("清理后的指数代码: {}", cleanedCode);
            } else {
                log.warn("指数代码不符合格式要求（必须只包含字母和数字）: {}", cleanedCode);
            }
        } else {
            log.warn("指数代码解析失败 - 可能原因: 列不存在或值为空");
            // 尝试从第一列获取代码，作为后备方案
            if (values.length > 0 && values[0] != null && !values[0].isEmpty()) {
                String backupCode = values[0].replaceAll("=\\s*[\"\']?|[\"\']", "");
                // 检查后备代码是否只包含字母和数字
                if (backupCode.matches("^[a-zA-Z0-9]+$")) {
                    indexModel.setCode(backupCode);
                    log.info("使用第一列值作为代码后备方案: {}", backupCode);
                } else {
                    log.warn("后备指数代码不符合格式要求（必须只包含字母和数字）: {}", backupCode);
                }
            }
        }
        
        // 名称解析
        String nameValue = headerMapping.getValue(values, "指数名称");
        if (nameValue != null) {
            indexModel.setName(nameValue);
        } else {
            log.warn("指数名称解析失败");
        }
        
        // 发布时间解析
        String publishDateStr = headerMapping.getValue(values, "发布时间");
        if (publishDateStr != null && !publishDateStr.trim().isEmpty()) {
            try {
                // 移除可能存在的等号和引号
                publishDateStr = publishDateStr.replaceAll("=\\s*[\"\']?|[\"\']", "");
                indexModel.setPublishDate(java.sql.Date.valueOf(publishDateStr));
            } catch (Exception e) {
                log.warn("发布时间格式解析失败: {}", publishDateStr, e);
            }
        }
        
        // 处理其他数值字段
        safelySetBigDecimal(indexModel::setChangeRate, headerMapping.getValue(values, "涨跌幅"));
        safelySetBigDecimal(indexModel::setClosePoint, headerMapping.getValue(values, "收盘点位"));
        safelySetBigDecimal(indexModel::setPeTtm, headerMapping.getValue(values, "PE-TTM(当前值)"));
        safelySetBigDecimal(indexModel::setPeTtmPercentile, headerMapping.getValue(values, "PE-TTM(分位点%)"));
        safelySetBigDecimal(indexModel::setPb, headerMapping.getValue(values, "PB(当前值)"));
        safelySetBigDecimal(indexModel::setPbPercentile, headerMapping.getValue(values, "PB(分位点%)"));
        safelySetBigDecimal(indexModel::setPsTtm, headerMapping.getValue(values, "PS-TTM(当前值)"));
        safelySetBigDecimal(indexModel::setPsTtmPercentile, headerMapping.getValue(values, "PS-TTM(分位点%)"));
        safelySetBigDecimal(indexModel::setDividendYield, headerMapping.getValue(values, "股息率"));
        
        return indexModel;
    }
    
    // 函数式接口，用于设置BigDecimal值
    @FunctionalInterface
    private interface BigDecimalSetter {
        void setValue(BigDecimal value);
    }
    
    // 安全地设置BigDecimal值
    private void safelySetBigDecimal(BigDecimalSetter setter, String value) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }
        try {
            // 清理数据，移除等号和引号
            String cleanedValue = value.replaceAll("=\\s*[\"\']?|[\"\']", "");
            setter.setValue(new BigDecimal(cleanedValue));
        } catch (NumberFormatException e) {
            log.warn("数值解析失败: {}", value, e);
        }
    }

    private String cleanValue(String v) {
        if (v == null) return null;
        return v.replaceFirst("^=", "");
    }

    private BigDecimal parseStringToBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return new BigDecimal(value.trim().replace("=", ""));
        } catch (NumberFormatException e) {
            log.warn("数值解析失败: {}", value, e);
            return null;
        }
    }
}
