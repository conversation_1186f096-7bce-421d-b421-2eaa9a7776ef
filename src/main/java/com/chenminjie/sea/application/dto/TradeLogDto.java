package com.chenminjie.sea.application.dto;

import lombok.Data;

import java.util.Date;

/**
 * @File: TradeLogDto.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/27 10:09
 **/
@Data
public class TradeLogDto {
    private Long id;

    private Long strategyId;

    private Long gridId;

    private Integer gridType;

    private Integer level;

    private Integer tradeType;

    private Integer tradeShares;

    private Integer tradePrice;

    private Integer reminderShares;

    private Integer theoreticalPrice;

    private Integer theoreticalShares;

    private Date tradeAt;

    private Date createAt;

    /**
     * 盈利
     */
    private Integer profit;

    /**
     * 盈利比
     */
    private Integer profitRatio;

    /**
     * 年化利率
     */
    private Integer apy;

    /**
     * 持有时间
     */
    private Integer holdTime;
}
