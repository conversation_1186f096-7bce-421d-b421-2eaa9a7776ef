package com.chenminjie.sea.application.dto.apple;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 苹果用户DTO
 */
@Data
public class AppleUserDto {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 苹果用户唯一标识
     */
    private String appleUserId;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 是否为苹果隐私邮箱
     */
    private Boolean isPrivateEmail;
    
    /**
     * 邮箱是否已验证
     */
    private Boolean emailVerified;
    
    /**
     * 真实用户状态
     */
    private Integer realUserStatus;
    
    /**
     * 用户全名
     */
    private String fullName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
} 