package com.chenminjie.sea.application.dto;

import lombok.Data;

import java.util.Date;

/**
 * @File: GridDto.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/12 16:36
 **/
@Data
public class GridDto {

    private Long id;

    private Long strategyId;

    private Integer gridType;

    private Integer level;

    private Integer holdShares;

    private Integer theoreticalBuyPrice;

    private Integer theoreticalBuyShares;

    private Integer theoreticalSellPrice;

    private Integer theoreticalSellShares;

    private Integer triggerAmount;

    private Integer buyPrice;

    private Integer status;

    private Date createAt;

    private Date updateAt;

    private Date buyAt;

    private String openId;

    private Boolean isDelete;
}
