package com.chenminjie.sea.application.dto.apple;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 苹果登录请求
 */
@Data
public class AppleLoginRequest {
    
    /**
     * 苹果返回的identityToken
     */
    @NotBlank(message = "identityToken不能为空")
    private String identityToken;
    
    /**
     * 用户全名（首次登录时提供）
     */
    private String fullName;
    
    /**
     * 用户邮箱（首次登录时提供）
     */
    private String email;
} 