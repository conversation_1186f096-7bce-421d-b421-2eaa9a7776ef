package com.chenminjie.sea.application.dto.apple;

import lombok.Builder;
import lombok.Data;

/**
 * 苹果登录响应
 */
@Data
@Builder
public class AppleLoginResponse {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 令牌类型
     */
    private String tokenType;
    
    /**
     * 访问令牌过期时间（秒）
     */
    private Long expiresIn;
    
    /**
     * 用户信息
     */
    private AppleUserDto user;
    
    /**
     * 是否为新用户
     */
    private Boolean isNewUser;
} 