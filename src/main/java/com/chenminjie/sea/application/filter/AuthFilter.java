package com.chenminjie.sea.application.filter;

import com.chenminjie.sea.application.dto.SessionDto;
import com.chenminjie.sea.application.service.SessionService;
import com.chenminjie.sea.common.constant.HttpHeader;
import com.chenminjie.sea.common.error.AuthenticationErrorCode;
import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Objects;

/**
 * @File: AuthFilter.java
 * @author: ChenMinJie
 * @create: 2023/05/15 22:02
 **/
@Order(value = Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@Component
public class AuthFilter extends OncePerRequestFilter {

    @Autowired
    private SessionService sessionService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String sessionKey = request.getHeader(HttpHeader.GSA_SESSION);
        if (StringUtils.hasLength(sessionKey)) {
            SessionDto sessionDto = sessionService.getSession(sessionKey);
            if (Objects.nonNull(sessionDto)) {
                UserContext.setSessionKey(sessionDto.getSessionKey());
                UserContext.setOpenId(sessionDto.getOpenId());
                filterChain.doFilter(request, response);
                return;
            } else {
                throw HttpClientErrorException.create(
                        HttpStatus.UNAUTHORIZED,
                        "Unauthorized",
                        new HttpHeaders(),
                        CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorBody("Illegal session key").toString().getBytes(),
                        Charset.defaultCharset());
            }
        }
        throw HttpClientErrorException.create(
                HttpStatus.UNAUTHORIZED,
                "Unauthorized",
                new HttpHeaders(),
                AuthenticationErrorCode.UNAUTHORIZED.getErrorBody("No session key").toString().getBytes(),
                Charset.defaultCharset());
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        log.info("request path: " + path);
        return path.startsWith("/sessions") || path.startsWith("/api/v1/stocks/") || path.startsWith("/api/v1/apple");
    }
}
