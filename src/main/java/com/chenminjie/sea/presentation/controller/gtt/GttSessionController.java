package com.chenminjie.sea.presentation.controller.gtt;

import com.chenminjie.sea.application.service.gtt.GttSessionService;
import com.chenminjie.sea.application.service.gtt.GttUserService;
import com.chenminjie.sea.common.dto.CreateGttSessionRequest;
import com.chenminjie.sea.common.dto.CreateSessionResponse;
import com.chenminjie.sea.domain.model.gtt.GttSessionModel;
import com.chenminjie.sea.domain.model.gtt.GttUserModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("api/v1/gtt/sessions")
public class GttSessionController {
    @Autowired
    private GttSessionService gttSessionService;
    @Autowired
    private GttUserService gttUserService;

    @PostMapping("")
    public ResponseEntity<CreateSessionResponse> createSession(@Valid @RequestBody CreateGttSessionRequest request) {
        GttUserModel gttUserModel = gttUserService.getUser(request.getAppleId());
        if (gttUserModel == null) {
            return ResponseEntity.badRequest().body(null);
        }

        GttSessionModel model = gttSessionService.createSession(request.getAppleId());
        return ResponseEntity.ok(CreateSessionResponse.builder()
                .sessionKey(model.getSessionKey())
                .expireAt(model.getExpireAt())
                .build());
    }
}
