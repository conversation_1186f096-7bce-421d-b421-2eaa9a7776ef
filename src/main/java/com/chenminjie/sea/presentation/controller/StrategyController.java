package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.FlexibleTradeLogDto;
import com.chenminjie.sea.application.dto.GridDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.application.service.GridService;
import com.chenminjie.sea.application.service.StrategyService;
import com.chenminjie.sea.common.dto.*;
import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.error.ErrorBody;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * @File: StrategyController.java
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2023/04/25 22:57
 **/
@Slf4j
@RestController
@RequestMapping("api/v1/strategies")
public class StrategyController {

    @Autowired
    private StrategyService strategyService;

    @Autowired
    private GridService gridService;

    @GetMapping("")
    public ResponseEntity<QueryStrategyResponse> getStrategies(@RequestHeader("GSA-Session") String session,
                                               @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                               @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        log.info(String.format("get strategy : pageNumber(%s), pageSize(%s)", pageNumber, pageSize));
        if (pageNumber <= 0 || pageSize <= 0 || pageSize > 100) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(strategyService.getPagedStrategies(UserContext.getOpenId(), pageNumber, pageSize), HttpStatus.OK);
    }

    @GetMapping("/app")
    public ResponseEntity<SyncStrategyResponse> getSyncStrategies(@RequestHeader("GSA-Session") String session,
                                                               @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                                               @RequestParam(value = "pageSize", defaultValue = "500") Integer pageSize) {
        log.info(String.format("getSyncStrategies : pageNumber(%s), pageSize(%s)", pageNumber, pageSize));
        if (pageNumber <= 0 || pageSize <= 0 || pageSize > 500) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
        QueryStrategyResponse strategyResponse = strategyService.getPagedStrategies(UserContext.getOpenId(), pageNumber, pageSize);
        Map<Long, List<GridDto>> strategyGridMap = new HashMap<>();
        Map<Long, List<TradeLogDto>> gridTradeLogMap = new HashMap<>();
        for (Strategy strategy : strategyResponse.getResult()) {
            List<GridDto> grids = gridService.getAllGridsWithInvalid(UserContext.getOpenId(), strategy.getId());
            strategyGridMap.put(strategy.getId(), grids);
            List<TradeLogDto> tradeLogs = strategyService.getStrategyTradeLogs(UserContext.getOpenId(), strategy.getId(), 1, 500).getResult();
            for (TradeLogDto tradeLog : tradeLogs) {
                List<TradeLogDto> tList = gridTradeLogMap.getOrDefault(tradeLog.getGridId(), new ArrayList<>());
                tList.add(tradeLog);
                gridTradeLogMap.put(tradeLog.getGridId(), tList);
            }
        }

        return new ResponseEntity<>(SyncStrategyResponse.from(strategyResponse, strategyGridMap, gridTradeLogMap), HttpStatus.OK);
    }

    @PostMapping("")
    public ResponseEntity<Strategy> createStrategy(@RequestHeader("GSA-Session") String session,
                                                   @Valid @RequestBody CreateStrategyRequest request) {
        log.info("create strategy request: " + request.toString());
        Strategy strategy;
        try {
            strategy = strategyService.createStrategy(request);
            return new ResponseEntity<>(strategy, HttpStatus.CREATED);
        } catch (WxErrorException e) {
            log.error("wx open platform error", e);
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.WX_SERVER_ERROR.getErrorCode())
                    .reason(CommonErrorCode.WX_SERVER_ERROR.getErrorMsg())
                    .detail("服务器繁忙,请稍后重试").build(), e, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (IllegalArgumentException e) {
            log.error("illegal argument", e);
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail(e.getMessage()).build(), e, HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/{strategyId}")
    public ResponseEntity<Void> updateStrategy(@RequestHeader("GSA-Session") String session,
                                                   @PathVariable("strategyId") Long strategyId,
                                                   @Valid @RequestBody UpdateStrategyRequest request) {
        log.info("update strategy: " + strategyId);
        log.info("update strategy request: " + request.toString());
        strategyService.updateStrategy(strategyId, request, UserContext.getOpenId());
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/{strategyId}")
    public ResponseEntity<Void> deleteStrategy(@RequestHeader("GSA-Session") String session,
                                               @PathVariable("strategyId") Long strategyId) {
        log.info("delete strategy: " + strategyId);
        strategyService.deleteStrategy(strategyId, UserContext.getOpenId());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{strategyId}")
    public ResponseEntity<Strategy> getStrategy(@RequestHeader("GSA-Session") String session,
                                               @PathVariable("strategyId") Long strategyId) {
        log.info("get strategy: " + strategyId);
        Strategy strategy = strategyService.getStrategy(UserContext.getOpenId(), strategyId);
        return ResponseEntity.ok(strategy);
    }

    @GetMapping("/{id}/tradeLogs")
    public ResponseEntity<QueryPagedResponse<TradeLogDto>> getGridGradeLog(@RequestHeader("GSA-Session") String session,
                                                                           @PathVariable("id") Long id,
                                                                           @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                                                           @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        log.info("get tradeLog, strategy id: " + id);
        log.info(String.format("get tradeLog : pageNumber(%s), pageSize(%s)", pageNumber, pageSize));
        QueryPagedResponse<TradeLogDto> tradeLogs = strategyService.getStrategyTradeLogs(UserContext.getOpenId(), id, pageNumber, pageSize);
        return ResponseEntity.ok(tradeLogs);
    }

    @GetMapping("/{id}/summary")
    public ResponseEntity<StrategySummary> summarizeStrategy(@RequestHeader("GSA-Session") String session,
                                                            @PathVariable("id") Long id) {
        log.info("summarize strategy: " + id);
        StrategySummary strategySummary = strategyService.getStrategySummary(UserContext.getOpenId(), id);
        return ResponseEntity.ok(strategySummary);
    }

    @PostMapping("/{id}/flexibleTradeLogs")
    public ResponseEntity<Void> recordFlexibleTradeLog(@RequestHeader("GSA-Session") String session,
                                                       @PathVariable("id") Long id,
                                                       @Valid @RequestBody TradeRetainSharesRequest request) {
        log.info("strategy id: " + id);
        log.info("TradeRetainSharesRequest: " + request);
        strategyService.tradeRetainShares(id, request);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/{id}/flexibleTradeLogs")
    public ResponseEntity<List<FlexibleTradeLogDto>> getFlexibleTradeLogs(@RequestHeader("GSA-Session") String session,
                                                                           @PathVariable("id") Long id) {
        log.info("strategy id: " + id);
        List<FlexibleTradeLogDto> res = strategyService.getFlexibleTradeLogs(id);
        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    /**
     * 批量获取策略的统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<QueryPagedResponse<StrategyStatisticsResponse>> getStrategyStatistics(
            @RequestHeader("GSA-Session") String session,
            @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        log.info("get strategy statistics: pageNumber({}), pageSize({})", pageNumber, pageSize);
        
        if (pageNumber <= 0 || pageSize <= 0 || pageSize > 100) {
            throw new InternalException(
                ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("无效的分页参数")
                    .build(),
                new IllegalArgumentException(),
                HttpStatus.BAD_REQUEST
            );
        }
        
        return new ResponseEntity<>(
            strategyService.getStrategyStatistics(UserContext.getOpenId(), pageNumber, pageSize),
            HttpStatus.OK
        );
    }
}
