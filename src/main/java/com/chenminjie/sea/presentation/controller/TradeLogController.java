package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.application.service.TradeLogService;
import com.chenminjie.sea.common.dto.QueryTradeLogsByDateRequest;
import com.chenminjie.sea.common.dto.UpdateTradeLogRequest;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @File: TradeLogController.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2025/02/28
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/tradelogs")
public class TradeLogController {

    @Autowired
    private TradeLogService tradeLogService;

    @PutMapping("/{id}")
    public ResponseEntity<Void> updateTradeLog(@RequestHeader("GSA-Session") String session,
                                               @PathVariable("id") Long id,
                                               @Valid @RequestBody UpdateTradeLogRequest request) {
        log.info(String.format("open id (%s) update trade log (%s)", UserContext.getOpenId(), id));
        log.info(request.toString());
        tradeLogService.updateTradeLog(UserContext.getOpenId(), id, request);
        return ResponseEntity.noContent().build();
    }

    /**
     * 获取交易日志
     * 支持以下查询模式：
     * 1. 不提供参数：返回所有日志
     * 2. 仅提供year：返回指定年份的日志
     * 3. 提供year和month：返回指定年月的日志
     * 4. 提供year、month和day：返回指定日期的日志
     *
     * @param session 用户会话信息
     * @param request 日期查询参数
     * @return 交易日志列表
     */
    @GetMapping("")
    public ResponseEntity<List<TradeLogDto>> getValidTradeLogsByDate(
            @RequestHeader("GSA-Session") String session,
            @Valid QueryTradeLogsByDateRequest request) {
        
        log.info("Query trade logs with params: {}", request);

        List<TradeLogDto> tradeLogs = tradeLogService.getValidTradeLogsByDate(
                UserContext.getOpenId(),
                request
        );

        return ResponseEntity.ok(tradeLogs);
    }
}
