package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.GridDto;
import com.chenminjie.sea.application.dto.TradeLogDto;
import com.chenminjie.sea.application.service.GridService;
import com.chenminjie.sea.common.dto.CreateGridRequest;
import com.chenminjie.sea.common.dto.DeleteGridRequest;
import com.chenminjie.sea.common.dto.UpdateGridRequest;
import com.chenminjie.sea.common.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;

/**
 * @File: GridController.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/12 16:38
 **/
@Slf4j
@RestController
@RequestMapping("api/v1/grids")
public class GridController {

    @Autowired
    private GridService gridService;

    @PostMapping("")
    public ResponseEntity<GridDto> createGrid(@RequestHeader("GSA-Session") String session,
                                              @Valid @RequestBody CreateGridRequest request) throws URISyntaxException {
        log.info("create grid request: " + request.toString());
        GridDto gridDto = gridService.buyGrid(request);
        return ResponseEntity.created(new URI("/grids/" + gridDto.getOpenId())).body(gridDto);
    }

    @GetMapping("")
    public ResponseEntity<List<GridDto>> getAllGrids(@RequestHeader("GSA-Session") String session,
                                                     @RequestParam(value = "strategyId") @NotNull Long strategyId) {
        log.info("get all grids: " + strategyId);
        List<GridDto> grids = gridService.getAllGrids(UserContext.getOpenId(), strategyId);
        return ResponseEntity.ok(grids);
    }

    @GetMapping("/{id}")
    public ResponseEntity<GridDto> getGrid(@RequestHeader("GSA-Session") String session,
                                           @PathVariable("id") Long id) {
        log.info("get grid: " + id);
        GridDto gridDto = gridService.getGrid(UserContext.getOpenId(), id);
        return ResponseEntity.ok(gridDto);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Void> updateGrid(@RequestHeader("GSA-Session") String session,
                                           @PathVariable("id") Long id,
                                           @Valid @RequestBody UpdateGridRequest request) {
        log.info(String.format("open id (%s) update grid (%s)", UserContext.getOpenId(), id));
        log.info(request.toString());
        gridService.updateGrid(UserContext.getOpenId(), id, request);
        return ResponseEntity.ok(null);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteGrid(@RequestHeader("GSA-Session") String session,
                                           @PathVariable("id") Long id,
                                           @Valid @RequestBody DeleteGridRequest request) {
        log.info(String.format("open id (%s) delete grid (%s), request: (%s)", UserContext.getOpenId(), id, request.toString()));
        if (Objects.nonNull(request.getIsDelete()) && request.getIsDelete()) {
            gridService.deleteGrid(id, UserContext.getOpenId());
        } else {
            gridService.sellGrid(id, request);
        }
        return ResponseEntity.ok(null);
    }

    @GetMapping("/{id}/tradeLogs")
    public ResponseEntity<List<TradeLogDto>> getGridGradeLog(@RequestHeader("GSA-Session") String session,
                                                             @PathVariable("id") Long id,
                                                             @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                                             @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        log.info("get tradeLog, grid id: " + id);
        List<TradeLogDto> tradeLogs = gridService.getGridTradeLogs(UserContext.getOpenId(), id);
        return ResponseEntity.ok(tradeLogs);
    }
}
