package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.dto.SessionDto;
import com.chenminjie.sea.application.service.SessionService;
import com.chenminjie.sea.application.service.StrategyService;
import com.chenminjie.sea.common.dto.CreateStrategyRequest;
import com.chenminjie.sea.common.dto.SessionRequest;
import com.chenminjie.sea.common.dto.SessionResponse;
import com.chenminjie.sea.domain.model.CreateSessionModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @File: SessionController.java
 * @author: Chen<PERSON>inJie
 * @create: 2023/04/14 00:03
 **/
@Slf4j
@RestController
public class SessionController {

    @Autowired
    private SessionService sessionService;

    @PostMapping("/sessions")
    public SessionResponse createSession(@RequestBody SessionRequest sessionRequest) {
        log.info("request code: " + sessionRequest.getCode());
        SessionDto sessionDto = sessionService.createSession(CreateSessionModel.builder().code(sessionRequest.getCode()).build());
        return SessionResponse.builder()
                .msg("ok")
                .result("success")
                .sessionKey(sessionDto.getSessionKey())
                .build();
    }
}
