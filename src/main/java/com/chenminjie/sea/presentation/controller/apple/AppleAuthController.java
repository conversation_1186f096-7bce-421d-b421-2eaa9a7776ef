package com.chenminjie.sea.presentation.controller.apple;

import com.chenminjie.sea.application.dto.apple.AppleLoginRequest;
import com.chenminjie.sea.application.dto.apple.AppleLoginResponse;
import com.chenminjie.sea.application.service.apple.AppleAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 苹果授权登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/apple")
public class AppleAuthController {
    
    @Autowired
    private AppleAuthService appleAuthService;
    
    /**
     * 苹果登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@Valid @RequestBody AppleLoginRequest request) {
        log.info("收到苹果登录请求:{}", request);
        
        try {
            AppleLoginResponse response = appleAuthService.login(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", response);
            
            log.info("苹果登录成功，用户ID: {}", response.getUser().getId());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("苹果登录失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Apple Auth Service is running");
        result.put("data", "OK");
        
        return ResponseEntity.ok(result);
    }
} 