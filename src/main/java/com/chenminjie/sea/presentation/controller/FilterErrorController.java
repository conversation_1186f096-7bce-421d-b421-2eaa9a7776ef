package com.chenminjie.sea.presentation.controller;

import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpClientErrorException;

import javax.servlet.http.HttpServletRequest;

/**
 * @File: ErrorController.java
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2023/07/11 20:56
 **/
@RestController
public class FilterErrorController {

    private static final String ERROR = "javax.servlet.error.exception";

    @SneakyThrows
    @RequestMapping("/500")
    public void handleError(HttpServletRequest request) {
        Throwable throwable = (Throwable) request.getAttribute(ERROR);
        throw tryGetHttpException(throwable);
    }

    private Throwable tryGetHttpException(Throwable throwable) {
        if (throwable != null && throwable.getCause() != throwable) {
            if (throwable instanceof HttpClientErrorException) {
                return throwable;
            }
            throwable = throwable.getCause();
        }
        return throwable;
    }
}
