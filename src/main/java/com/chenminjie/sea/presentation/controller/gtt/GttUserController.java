package com.chenminjie.sea.presentation.controller.gtt;

import com.chenminjie.sea.application.service.gtt.GttUserService;
import com.chenminjie.sea.common.dto.CreateGttUserRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("api/v1/gtt/user")
public class GttUserController {
    @Autowired
    private GttUserService gttUserService;

    @PostMapping("")
    public ResponseEntity<Void> createUser(@Valid @RequestBody CreateGttUserRequest request) {
        gttUserService.createUser(request);
        return ResponseEntity.ok().build();
    }
}
