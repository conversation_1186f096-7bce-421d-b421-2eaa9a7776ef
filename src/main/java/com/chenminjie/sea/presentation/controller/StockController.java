package com.chenminjie.sea.presentation.controller;

import com.chenminjie.sea.application.service.StockService;
import com.chenminjie.sea.common.dto.SearchStockResponse;
import com.chenminjie.sea.common.dto.StockFilterRequest;
import com.chenminjie.sea.common.dto.StockIndexResponse;
import com.chenminjie.sea.common.dto.StocksPriceResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @File: StockController.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/12 23:08
 **/
@Slf4j
@RestController
@RequestMapping("api/v1/stocks")
public class StockController {

    @Autowired
    private StockService stockService;

    @GetMapping("/init")
    public void init(@RequestParam String token) {
        if ("CmjSeaInit24".equals(token)) {
            stockService.initFinancialData();
        } else {
            log.error("invalid token: {}", token);
        }
    }

    @GetMapping("/")
    public ResponseEntity<SearchStockResponse> searchStock(@RequestParam(value = "searchCode") String codePrefix,
                                                           @RequestParam("token") String token) {
        log.info("get stock: " + codePrefix);
        if (!token.equals("cmj")) {
            return ResponseEntity.badRequest().build();
        }
        if (Objects.isNull(codePrefix)) {
            return ResponseEntity.badRequest().build();
        }
        return ResponseEntity.ok(new SearchStockResponse(stockService.searchByCode(codePrefix), ""));
    }

//    @GetMapping("/{code}")
//    public ResponseEntity<StockPriceResponse> getPrice(@PathVariable("code") String code) {
//        log.info("get stock price: " + code);
//        if (Objects.isNull(code)) {
//            return ResponseEntity.badRequest().build();
//        }
//        return ResponseEntity.ok(stockService.queryPrice(code));
//    }

    @GetMapping("/batch")
    public ResponseEntity<StocksPriceResponse> getStocksPrice(@RequestParam("codes") String codes,
                                                              @RequestParam("token") String token) {
        long start = System.currentTimeMillis();
        log.info("get stocks price: " + codes);
        if (!token.equals("cmj")) {
            return ResponseEntity.badRequest().build();
        }
        if (Objects.isNull(codes)) {
            return ResponseEntity.badRequest().build();
        }
        Map<String, String> codeMapping = new HashMap<>();
        Set<String> formatCodes = Arrays.stream(codes.split(","))
                .map(code -> {
                    String formatCode = code.split("\\.")[0];
                    codeMapping.put(formatCode, code);
                    return formatCode;
                })
                .collect(Collectors.toSet());
                
        StocksPriceResponse res = stockService.queryStocksPrice(formatCodes);
        res.getSuccess().forEach(stock -> {
            String originalCode = codeMapping.get(stock.getCode());
            if (originalCode != null) {
                stock.setCode(originalCode);
            }
        });
        
        long end = System.currentTimeMillis();
        log.info("cost time: " + (end - start));
        return ResponseEntity.ok(res);
    }

    @GetMapping("/index")
    public ResponseEntity<StockIndexResponse> getIndex(@RequestParam("codes") String codes,
                                                        @RequestParam("token") String token,
                                                        @RequestParam(required = false, defaultValue = "ewpvo") String metricType,
                                                        @RequestParam(required = false, defaultValue = "false") Boolean isSearch) {
        if (!token.equals("cmj")) {
            return ResponseEntity.badRequest().build();
        }
        
        Set<String> metricTypes = Arrays.stream(metricType.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
                
        if (metricTypes.isEmpty()) {
            metricTypes = Collections.singleton("ewpvo"); // 默认值
        }
        
        StockIndexResponse response = new StockIndexResponse();
        
        if (isSearch) {
            for (String type : metricTypes) {
                StockIndexResponse result = stockService.searchStockIndex(codes, type);
                result.getSuccess().forEach(index -> index.setMetricType(type));
                response.getSuccess().addAll(result.getSuccess());
                response.getFailed().addAll(result.getFailed());
            }
            return ResponseEntity.ok(response);
        }
        
        Set<String> stockCodes = Arrays.stream(codes.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
                
        for (String type : metricTypes) {
            StockIndexResponse result = stockService.queryStockIndex(stockCodes, type);
            result.getSuccess().forEach(index -> index.setMetricType(type));
            response.getSuccess().addAll(result.getSuccess());
            response.getFailed().addAll(result.getFailed());
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/index")
    public ResponseEntity<String> uploadCsvFile(@RequestParam("file") MultipartFile file,
                                                @RequestParam("token") String token,
                                                @RequestParam("metricType") String metricType,
                                                @RequestParam("periodYear") Integer periodYear) {
        if (!token.equals("cmj")) {
            return ResponseEntity.badRequest().build();
        }
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("请选择要上传的文件");
        }
        
        if (!file.getOriginalFilename().endsWith(".csv")) {
            return ResponseEntity.badRequest().body("只支持CSV文件格式");
        }
        
        try {
            stockService.updateStockIndex(file, metricType, periodYear);
            return ResponseEntity.ok("文件上传并处理成功");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("文件处理失败: " + e.getMessage());
        }
    }
    
    // @GetMapping("/filter")
    // @Deprecated
    // public ResponseEntity<StockIndexResponse> filterStocks(@RequestParam("token") String token,
    //                                                      @RequestParam Double fiveYearPercentile,
    //                                                      @RequestParam Double tenYearPercentile) {
    //     if (!token.equals("cmj")) {
    //         return ResponseEntity.badRequest().build();
    //     }
        
    //     if (fiveYearPercentile < 0 || fiveYearPercentile > 100 ||
    //         tenYearPercentile < 0 || tenYearPercentile > 100) {
    //         return ResponseEntity.badRequest().build();
    //     }
        
    //     return ResponseEntity.ok(stockService.filterByPercentile(fiveYearPercentile, tenYearPercentile));
    // }

    @PostMapping("/filter")
    public ResponseEntity<StockIndexResponse> filterStocksV2(@RequestParam("token") String token,
                                                           @RequestBody StockFilterRequest request) {
        if (!token.equals("cmj")) {
            return ResponseEntity.badRequest().build();
        }

        if (!request.isValid()) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(stockService.filterStocks(request));
    }
}
