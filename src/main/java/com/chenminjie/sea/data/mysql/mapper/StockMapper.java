package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.StockEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @File: StockMapper
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/13 08:23
 **/
@Mapper
public interface StockMapper {

    @Insert("<script>" +
            "INSERT INTO stock(code, name, type, market) VALUES " +
            "<foreach item='stockEntity' collection='stockEntities' separator=','>" +
            "(#{stockEntity.code}, #{stockEntity.name}, #{stockEntity.type}, #{stockEntity.market})" +
            "</foreach> " +
            "ON DUPLICATE KEY UPDATE " +
            "name = VALUES(name), " +
            "type = VALUES(type)" +
            "</script>")
    int batchUpsert(@Param("stockEntities") List<StockEntity> stockEntities);

    @Select("SELECT * FROM stock WHERE code LIKE #{codePrefix} limit 10")
    List<StockEntity> searchByCode(@Param("codePrefix") String codePrefix);

    @Select("<script>SELECT * FROM stock WHERE code in " +
            "(<foreach item='code' collection='codes' separator=','>" +
            "#{code}" +
            "</foreach>)</script>")
    @Results(id = "stockResult", value = {
        @Result(property = "code", column = "code"),
        @Result(property = "name", column = "name"),
        @Result(property = "type", column = "type"),
        @Result(property = "market", column = "market"),
        @Result(property = "maxValue", column = "max_value"),
        @Result(property = "minValue", column = "min_value"),
        @Result(property = "createAt", column = "create_at"),
        @Result(property = "updateAt", column = "update_at")
    })
    List<StockEntity> getStocksByCodes(@Param("codes") List<String> codes);     
}
