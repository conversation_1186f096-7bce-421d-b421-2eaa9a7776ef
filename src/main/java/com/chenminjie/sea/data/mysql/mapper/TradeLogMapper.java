package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.TradeLogEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * @File: TradeLogMapper.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/23 20:32
 **/
public interface TradeLogMapper {

    @Insert("insert into trade_log(strategy_id, grid_id, grid_type, level, trade_type, trade_shares," +
            " trade_price, reminder_shares, theoretical_price, theoretical_shares, profit, trade_at, open_id)" +
            "values(#{strategyId}, #{gridId}, #{gridType}, #{level}, #{tradeType}, #{tradeShares}," +
            " #{tradePrice}, #{reminderShares}, #{theoreticalPrice}, #{theoreticalShares}, #{profit}, #{tradeAt}, #{openId})")
    Long insert(TradeLogEntity tradeLog);

    @Select("SELECT * FROM trade_log WHERE id = #{id} and open_id = #{openId}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridId", column = "grid_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "tradeType", column = "trade_type"),
            @Result(property = "tradeShares", column = "trade_shares"),
            @Result(property = "tradePrice", column = "trade_price"),
            @Result(property = "reminderShares", column = "reminder_shares"),
            @Result(property = "theoreticalPrice", column = "theoretical_price"),
            @Result(property = "theoreticalShares", column = "theoretical_shares"),
            @Result(property = "profit", column = "profit"),
            @Result(property = "tradeAt", column = "trade_at"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    TradeLogEntity findById(String openId, Long id);

    @Select("SELECT * FROM trade_log WHERE open_id = #{openId} AND grid_id = #{gridId} and is_delete = 0")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridId", column = "grid_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "tradeType", column = "trade_type"),
            @Result(property = "tradeShares", column = "trade_shares"),
            @Result(property = "tradePrice", column = "trade_price"),
            @Result(property = "reminderShares", column = "reminder_shares"),
            @Result(property = "theoreticalPrice", column = "theoretical_price"),
            @Result(property = "theoreticalShares", column = "theoretical_shares"),
            @Result(property = "profit", column = "profit"),
            @Result(property = "tradeAt", column = "trade_at"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    List<TradeLogEntity> findByOpenIdAndGridId(String openId, Long gridId);

    @Select("SELECT * FROM trade_log WHERE open_id = #{openId} AND strategy_id = #{strategyId} and is_delete = 0 order by trade_at desc, create_at desc")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridId", column = "grid_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "tradeType", column = "trade_type"),
            @Result(property = "tradeShares", column = "trade_shares"),
            @Result(property = "tradePrice", column = "trade_price"),
            @Result(property = "reminderShares", column = "reminder_shares"),
            @Result(property = "theoreticalPrice", column = "theoretical_price"),
            @Result(property = "theoreticalShares", column = "theoretical_shares"),
            @Result(property = "profit", column = "profit"),
            @Result(property = "tradeAt", column = "trade_at"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    List<TradeLogEntity> findByOpenIdAndStrategyId(String openId, Long strategyId);

    @Update("update trade_log set is_delete = 1 where id = #{id} and open_id = #{openId}")
    int deleteByIdAndOpenId(Long id, String openId);

    @Update("update trade_log set is_delete = 1 where grid_id = #{gridId} and open_id = #{openId}")
    int deleteByGridIdAndOpenId(Long gridId, String openId);

    @Update("<script>update trade_log " +
            "<set> " +
            "<if test='tradeShares != null'> trade_shares = #{tradeShares},</if> " +
            "<if test='tradePrice != null'> trade_price = #{tradePrice},</if> " +
            "<if test='tradeAt != null'> trade_at = #{tradeAt},</if> " +
            "<if test='profit != null'> profit = #{profit},</if> " +
            "<if test='reminderShares != null'> reminder_shares = #{reminderShares},</if> " +
            "</set> " +
            "where id = #{id} and open_id = #{openId} </script>")
    int updateTradeLog(TradeLogEntity tradeLog);

    @Select("SELECT * FROM trade_log " +
            "WHERE open_id = #{openId} " +
            "AND trade_at >= #{startDate} " +
            "AND trade_at < #{endDate} " +
            "AND is_delete = 0 " +
            "ORDER BY trade_at DESC, create_at DESC")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridId", column = "grid_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "tradeType", column = "trade_type"),
            @Result(property = "tradeShares", column = "trade_shares"),
            @Result(property = "tradePrice", column = "trade_price"),
            @Result(property = "reminderShares", column = "reminder_shares"),
            @Result(property = "theoreticalPrice", column = "theoretical_price"),
            @Result(property = "theoreticalShares", column = "theoretical_shares"),
            @Result(property = "profit", column = "profit"),
            @Result(property = "tradeAt", column = "trade_at"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    List<TradeLogEntity> findByOpenIdAndDateRange(String openId, Date startDate, Date endDate);
}
