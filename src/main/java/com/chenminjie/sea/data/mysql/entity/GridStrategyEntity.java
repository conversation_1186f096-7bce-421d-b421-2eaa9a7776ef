package com.chenminjie.sea.data.mysql.entity;

import lombok.Data;

import java.util.Date;

/**
 * @File: GridStrategyEntity.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/25 21:57
 **/
@Data
public class GridStrategyEntity {

    private Long id;

    private String openId;

    private String name;

    private String code;

    private Integer interval;

    private Integer targetPrice;

    private Integer amount;

    private Integer maxFall;

    private Integer sellGrid;

    private Integer buyGrid;

    private Integer sellPrice;

    private Integer buyPrice;

    private Integer buyStrategy;

    private Integer sellStrategy;

    private Integer incrementalBuyRatio;

    private Boolean mediumLargeSwitch;

    private Integer mediumInterval;

    private Integer largeInterval;

    private Integer remainderShares;

    private String unionId;

    private Date createAt;

    private Date updateAt;
}
