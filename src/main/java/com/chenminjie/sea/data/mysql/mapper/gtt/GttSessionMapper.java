package com.chenminjie.sea.data.mysql.mapper.gtt;

import com.chenminjie.sea.data.mysql.entity.gtt.GttSessionEntity;
import org.apache.ibatis.annotations.*;

@Mapper
public interface GttSessionMapper {

    @Insert("insert into gtt_session (session_key, user_id, is_valid, expire_at) values (#{sessionKey}, #{userId}, #{isValid}), #{expireAt}")
    @Options(useGeneratedKeys=true, keyProperty="id")
    int insert(GttSessionEntity gttSessionEntity);

    @Select("select * from gtt_session where session_key = #{sessionKey}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "sessionKey", column = "session_key"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "isValid", column = "is_valid"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
            @Result(property = "expireAt", column = "expire_at")
    })
    GttSessionEntity findBySessionKey(String sessionKey);
}
