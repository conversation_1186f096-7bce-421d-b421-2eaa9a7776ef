package com.chenminjie.sea.data.mysql.entity.apple;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 苹果用户实体
 */
@Data
public class AppleUserEntity {
    
    private Long id;
    
    /**
     * 苹果用户唯一标识 (JWT中的sub字段)
     */
    private String appleUserId;
    
    /**
     * 用户邮箱 (可能是隐私邮箱)
     */
    private String email;
    
    /**
     * 是否为苹果隐私邮箱
     */
    private Boolean isPrivateEmail;
    
    /**
     * 邮箱是否已验证
     */
    private Boolean emailVerified;
    
    /**
     * 真实用户状态: 0-不支持, 1-未知, 2-真实用户
     */
    private Integer realUserStatus;
    
    /**
     * 用户全名
     */
    private String fullName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
} 