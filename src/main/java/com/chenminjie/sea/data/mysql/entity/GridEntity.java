package com.chenminjie.sea.data.mysql.entity;

import lombok.Data;

import java.util.Date;

/**
 * @File: GridEntity.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/09 23:28
 **/
@Data
public class GridEntity {

    private Long id;
    private Long strategyId;
    private Integer gridType;
    private Integer level;
    private Integer holdShares;
    private Integer theoreticalBuyPrice;
    private Integer theoreticalBuyShares;
    private Integer theoreticalSellPrice;
    private Integer theoreticalSellShares;
    private Integer triggerAmount;
    private Integer buyPrice;
    private Integer status;
    private Date createAt;
    private Date updateAt;
    private Date buyAt;
    private String openId;
    private Boolean isDelete;

}
