package com.chenminjie.sea.data.mysql.mapper.apple;

import com.chenminjie.sea.data.mysql.entity.apple.AppleUserEntity;
import org.apache.ibatis.annotations.*;

/**
 * 苹果用户Mapper
 */
@Mapper
public interface AppleUserMapper {
    
    /**
     * 根据苹果用户ID查询用户
     */
    @Select("SELECT * FROM apple_users WHERE apple_user_id = #{appleUserId}")
    AppleUserEntity findByAppleUserId(@Param("appleUserId") String appleUserId);
    
    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM apple_users WHERE email = #{email}")
    AppleUserEntity findByEmail(@Param("email") String email);
    
    /**
     * 插入新用户
     */
    @Insert("INSERT INTO apple_users (apple_user_id, email, is_private_email, email_verified, " +
            "real_user_status, full_name, created_time, updated_time) " +
            "VALUES (#{appleUserId}, #{email}, #{isPrivateEmail}, #{emailVerified}, " +
            "#{realUserStatus}, #{fullName}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AppleUserEntity entity);
    
    /**
     * 更新用户信息
     */
    @Update("UPDATE apple_users SET email = #{email}, is_private_email = #{isPrivateEmail}, " +
            "email_verified = #{emailVerified}, real_user_status = #{realUserStatus}, " +
            "full_name = #{fullName}, updated_time = NOW() " +
            "WHERE apple_user_id = #{appleUserId}")
    int updateByAppleUserId(AppleUserEntity entity);
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM apple_users WHERE id = #{id}")
    AppleUserEntity findById(@Param("id") Long id);
} 