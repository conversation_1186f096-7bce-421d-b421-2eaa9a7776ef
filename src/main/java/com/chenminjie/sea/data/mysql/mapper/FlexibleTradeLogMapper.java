package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.FlexibleTradeLogEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface FlexibleTradeLogMapper {

    @Select("Select * from flexible_trade_log where strategy_id = #{strategyId} and open_id = #{openId}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "tradeType", column = "trade_type"),
            @Result(property = "tradeShares", column = "trade_shares"),
            @Result(property = "tradePrice", column = "trade_price"),
            @Result(property = "tradeAt", column = "trade_at"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "openId", column = "open_id")
    })
    List<FlexibleTradeLogEntity> findByStrategyIdAndOpenId(Long strategyId, String openId);

    @Insert("INSERT INTO flexible_trade_log(strategy_id, trade_type, trade_shares, trade_price, trade_at, open_id)" +
            " VALUES (#{strategyId}, #{tradeType}, #{tradeShares}, #{tradePrice}, #{tradeAt}, #{openId})")
    Long insert(FlexibleTradeLogEntity flexibleTradeLogEntity);
}
