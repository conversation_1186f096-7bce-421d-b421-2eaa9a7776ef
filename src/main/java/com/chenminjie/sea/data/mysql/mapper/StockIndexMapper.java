package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.StockIndexEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface StockIndexMapper {

    @Insert({
        "<script>",
        "INSERT INTO stock_index (index_code, index_name, publish_date, change_rate, close_point, period_year, metric_type,",
        "pe_ttm, pe_ttm_percentile, pb, pb_percentile, ps_ttm, ps_ttm_percentile, dividend_yield)",
        "VALUES",
        "<foreach collection='list' item='item' separator=','>",
        "(",
        "#{item.code}, #{item.name}, #{item.publishDate}, #{item.changeRate},",
        "#{item.closePoint}, #{item.periodYear}, #{item.metricType}, #{item.peTtm},",
        "#{item.peTtmPercentile}, #{item.pb}, #{item.pbPercentile}, #{item.psTtm},",
        "#{item.psTtmPercentile}, #{item.dividendYield}",
        ")",
        "</foreach>",
        "ON DUPLICATE KEY UPDATE",
        "index_name = VALUES(index_name),",
        "change_rate = VALUES(change_rate),",
        "close_point = VALUES(close_point),",
        "period_year = VALUES(period_year),",
        "metric_type = VALUES(metric_type),",
        "pe_ttm = VALUES(pe_ttm),",
        "pe_ttm_percentile = VALUES(pe_ttm_percentile),",
        "pb = VALUES(pb),",
        "pb_percentile = VALUES(pb_percentile),",
        "ps_ttm = VALUES(ps_ttm),",
        "ps_ttm_percentile = VALUES(ps_ttm_percentile),",
        "dividend_yield = VALUES(dividend_yield)",
        "</script>"
    })
    int batchUpsert(@Param("list") List<StockIndexEntity> list);

    @Select("<script>" +
            "SELECT * FROM stock_index WHERE index_code IN " +
            "(<foreach item='code' collection='codes' separator=','>" +
            "#{code}" +
            "</foreach>) " +
            "AND metric_type = #{metricType} " +
            "AND period_year = #{periodYear} </script>")
    @Results(id = "stockIndexResult", value = {
        @Result(property = "code", column = "index_code"),
        @Result(property = "name", column = "index_name"),
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "changeRate", column = "change_rate"),
        @Result(property = "closePoint", column = "close_point"),
        @Result(property = "periodYear", column = "period_year"),
        @Result(property = "metricType", column = "metric_type"),
        @Result(property = "peTtm", column = "pe_ttm"),
        @Result(property = "peTtmPercentile", column = "pe_ttm_percentile"),
        @Result(property = "pb", column = "pb"),
        @Result(property = "pbPercentile", column = "pb_percentile"),
        @Result(property = "psTtm", column = "ps_ttm"),
        @Result(property = "psTtmPercentile", column = "ps_ttm_percentile"),
        @Result(property = "dividendYield", column = "dividend_yield"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<StockIndexEntity> getIndexByCodes(@Param("codes") List<String> codes, 
                                          @Param("metricType") String metricType, 
                                          @Param("periodYear") Integer periodYear);

    @Select("SELECT * FROM stock_index WHERE " +
            "(index_code LIKE CONCAT(#{codePrefix}, '%') OR " +
            "index_name LIKE CONCAT('%', #{codePrefix}, '%')) " +
            "AND metric_type = #{metricType} " +
            "AND period_year = #{periodYear} " +
            "ORDER BY index_code " +
            "LIMIT 10")
    @ResultMap("stockIndexResult")
    List<StockIndexEntity> searchStockIndex(@Param("codePrefix") String codePrefix,
                                          @Param("metricType") String metricType,
                                          @Param("periodYear") Integer periodYear);

    @Deprecated
    @Select("SELECT * FROM stock_index WHERE " +
            "metric_type = #{metricType} AND " +
            "((period_year = 5 AND " +
            "pe_ttm_percentile <= #{fiveYearPercentile} AND " +
            "pb_percentile <= #{fiveYearPercentile} AND " +
            "ps_ttm_percentile <= #{fiveYearPercentile}) OR " +
            "(period_year = 10 AND " +
            "pe_ttm_percentile <= #{tenYearPercentile} AND " +
            "pb_percentile <= #{tenYearPercentile} AND " +
            "ps_ttm_percentile <= #{tenYearPercentile})) ")
    @ResultMap("stockIndexResult")
    List<StockIndexEntity> filterByPercentile(@Param("metricType") String metricType,
                                            @Param("fiveYearPercentile") Double fiveYearPercentile,
                                            @Param("tenYearPercentile") Double tenYearPercentile);

    @Select("<script>" +
            "SELECT * FROM stock_index WHERE metric_type = #{metricType} AND index_code IN (" +
            "  SELECT a.index_code" +
            "  FROM stock_index a" +
            "  JOIN stock_index b ON a.index_code = b.index_code" +
            "  WHERE a.metric_type = #{metricType}" +
            "  AND b.metric_type = #{metricType}" +
            "  AND a.period_year = 5" +
            "  AND b.period_year = 10" +
            "  AND (" +
            "    <trim prefixOverrides='AND'>" +
            "      <if test='pePercentile5Y != null'> AND a.pe_ttm_percentile &lt;= #{pePercentile5Y}</if>" +
            "      <if test='pbPercentile5Y != null'> AND a.pb_percentile &lt;= #{pbPercentile5Y}</if>" +
            "      <if test='pePercentile10Y != null'> AND b.pe_ttm_percentile &lt;= #{pePercentile10Y}</if>" +
            "      <if test='pbPercentile10Y != null'> AND b.pb_percentile &lt;= #{pbPercentile10Y}</if>" +
            "    </trim>" +
            "  )" +
            ")" +
            "ORDER BY index_code, period_year" +
            "</script>")
    @ResultMap("stockIndexResult")
    List<StockIndexEntity> filterByMultiplePercentiles(
            @Param("metricType") String metricType,
            @Param("pePercentile5Y") Double pePercentile5Y,
            @Param("pePercentile10Y") Double pePercentile10Y,
            @Param("pbPercentile5Y") Double pbPercentile5Y,
            @Param("pbPercentile10Y") Double pbPercentile10Y);
}
