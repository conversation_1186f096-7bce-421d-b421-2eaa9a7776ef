package com.chenminjie.sea.data.mysql.entity;

import lombok.Data;

import java.util.Date;

/**
 * @File: TradeLogEntity.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/23 19:57
 **/
@Data
public class TradeLogEntity {

    private Long id;

    private Long strategyId;

    private Long gridId;

    private Integer gridType;

    private Integer level;

    private Integer tradeType;

    private Integer tradeShares;

    private Integer tradePrice;

    private Integer reminderShares;

    private Integer theoreticalPrice;

    private Integer theoreticalShares;

    private Integer profit;

    private Date tradeAt;

    private Date createAt;

    private String openId;

    private Boolean isDelete;
}
