package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.GridEntity;
import com.chenminjie.sea.data.mysql.entity.GridStrategyEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * @File: GridStrategyMapper
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/25 21:32
 * @update: 2025/03/02
 **/
@Mapper
public interface GridStrategyMapper {

    @Select("SELECT * FROM grid_strategy WHERE open_id = #{openId} and is_delete = 0")
    @Results(value = {
            @Result(property = "openId", column = "open_id"),
            @Result(property = "targetPrice", column = "target_price"),
            @Result(property = "maxFall", column = "max_fall"),
            @Result(property = "sellGrid", column = "sell_grid"),
            @Result(property = "buyGrid", column = "buy_grid"),
            @Result(property = "sellPrice", column = "sell_price"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "buyStrategy", column = "buy_strategy"),
            @Result(property = "sellStrategy", column = "sell_strategy"),
            @Result(property = "incrementalBuyRatio", column = "incremental_buy_ratio"),
            @Result(property = "mediumLargeSwitch", column = "medium_large_switch"),
            @Result(property = "mediumInterval", column = "medium_interval"),
            @Result(property = "largeInterval", column = "large_interval"),
            @Result(property = "remainderShares", column = "remainder_shares"),
            @Result(property = "unionId", column = "union_id"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    List<GridStrategyEntity> findByOpenId(@Param("openId") String openId);

    @Select("SELECT * FROM grid_strategy WHERE open_id = #{openId} and id = #{id} and is_delete = 0")
    @Results(value = {
            @Result(property = "openId", column = "open_id"),
            @Result(property = "targetPrice", column = "target_price"),
            @Result(property = "maxFall", column = "max_fall"),
            @Result(property = "sellGrid", column = "sell_grid"),
            @Result(property = "buyGrid", column = "buy_grid"),
            @Result(property = "sellPrice", column = "sell_price"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "buyStrategy", column = "buy_strategy"),
            @Result(property = "sellStrategy", column = "sell_strategy"),
            @Result(property = "incrementalBuyRatio", column = "incremental_buy_ratio"),
            @Result(property = "mediumLargeSwitch", column = "medium_large_switch"),
            @Result(property = "mediumInterval", column = "medium_interval"),
            @Result(property = "largeInterval", column = "large_interval"),
            @Result(property = "remainderShares", column = "remainder_shares"),
            @Result(property = "unionId", column = "union_id"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    GridStrategyEntity findByIdAndOpenId(Long id, String openId);

    @Select("SELECT * FROM grid_strategy WHERE open_id = #{openId} and id = #{id} and is_delete = 0 for update")
    @Results(value = {
            @Result(property = "openId", column = "open_id"),
            @Result(property = "targetPrice", column = "target_price"),
            @Result(property = "maxFall", column = "max_fall"),
            @Result(property = "sellGrid", column = "sell_grid"),
            @Result(property = "buyGrid", column = "buy_grid"),
            @Result(property = "sellPrice", column = "sell_price"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "buyStrategy", column = "buy_strategy"),
            @Result(property = "sellStrategy", column = "sell_strategy"),
            @Result(property = "incrementalBuyRatio", column = "incremental_buy_ratio"),
            @Result(property = "mediumLargeSwitch", column = "medium_large_switch"),
            @Result(property = "mediumInterval", column = "medium_interval"),
            @Result(property = "largeInterval", column = "large_interval"),
            @Result(property = "remainderShares", column = "remainder_shares"),
            @Result(property = "unionId", column = "union_id"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    GridStrategyEntity selectForUpdate(Long id, String openId);

    @Insert("INSERT INTO grid_strategy(" +
            "open_id, name, code, `interval`, target_price, amount, max_fall, sell_grid, buy_grid," +
            " sell_price, buy_price, buy_strategy, sell_strategy, union_id, incremental_buy_ratio, medium_large_switch, medium_interval, large_interval) values(" +
            "#{openId}, #{name}, #{code}, #{interval}, #{targetPrice}, #{amount}, #{maxFall}, #{sellGrid}, #{buyGrid}, " +
            "#{sellPrice}, #{buyPrice}, #{buyStrategy}, #{sellStrategy}, #{unionId}, #{incrementalBuyRatio}, #{mediumLargeSwitch}, #{mediumInterval}, #{largeInterval})")
    Long insert(GridStrategyEntity gridStrategyEntity);

    @Update("update grid_strategy set is_delete = 1 where id = #{id} and open_id = #{openId}")
    int delete(Long id, String openId);

    @Select("SELECT * FROM grid_strategy WHERE open_id = #{openId} and id = #{strategyId} and is_delete = 0")
    @Results(value = {
            @Result(property = "openId", column = "open_id"),
            @Result(property = "targetPrice", column = "target_price"),
            @Result(property = "maxFall", column = "max_fall"),
            @Result(property = "sellGrid", column = "sell_grid"),
            @Result(property = "buyGrid", column = "buy_grid"),
            @Result(property = "sellPrice", column = "sell_price"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "buyStrategy", column = "buy_strategy"),
            @Result(property = "sellStrategy", column = "sell_strategy"),
            @Result(property = "incrementalBuyRatio", column = "incremental_buy_ratio"),
            @Result(property = "mediumLargeSwitch", column = "medium_large_switch"),
            @Result(property = "mediumInterval", column = "medium_interval"),
            @Result(property = "largeInterval", column = "large_interval"),
            @Result(property = "remainderShares", column = "remainder_shares"),
            @Result(property = "unionId", column = "union_id"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    GridStrategyEntity findByOpenIdAndStrategyId(@Param("openId") String openId, @Param("strategyId") Long strategyId);

    @Update("update grid_strategy set remainder_shares = #{remainderShares}, update_at = #{updateAt} where id = #{id} and open_id = #{openId}")
    int updateRemainderShares(GridStrategyEntity gridStrategyEntity);

    @Update("<script>update grid_strategy " +
            " <set>" +
            " <if test='name != null'>`name` = #{name},</if>" +
            " <if test='code != null'>code = #{code},</if>" +
            " <if test='interval != null'>`interval` = #{interval},</if>" +
            " <if test='targetPrice != null'>target_price = #{targetPrice},</if>" +
            " <if test='amount != null'>amount = #{amount},</if>" +
            " <if test='maxFall != null'>max_fall = #{maxFall},</if>" +
            " <if test='sellGrid != null'>sell_grid = #{sellGrid},</if>" +
            " <if test='buyGrid != null'>buy_grid = #{buyGrid},</if>" +
            " <if test='sellPrice != null'>sell_price = #{sellPrice},</if>" +
            " <if test='buyPrice != null'>buy_price = #{buyPrice},</if>" +
            " <if test='buyStrategy != null'>buy_strategy = #{buyStrategy},</if>" +
            " <if test='sellStrategy != null'>sell_strategy = #{sellStrategy},</if>" +
            " <if test='incrementalBuyRatio != null'>incremental_buy_ratio = #{incrementalBuyRatio},</if>" +
            " <if test='mediumLargeSwitch != null'>medium_large_switch = #{mediumLargeSwitch},</if>" +
            " <if test='mediumInterval != null'>medium_interval = #{mediumInterval},</if>" +
            " <if test='largeInterval != null'>large_interval = #{largeInterval},</if>" +
            " <if test='remainderShares != null'>remainder_shares = #{remainderShares},</if>" +
            " <if test='updateAt != null'>update_at = #{updateAt},</if>" +
            " </set>" +
            " where id = #{id} and open_id = #{openId} </script>"
    )
    int update(GridStrategyEntity gridStrategyEntity);

    @Select("<script>" +
            "SELECT gs.id as strategy_id, " +
            "SUM(tl.trade_price * tl.trade_shares) as total_investment " +
            "FROM grid_strategy gs " +
            "INNER JOIN trade_log tl ON tl.strategy_id = gs.id " +
            "WHERE gs.id IN " +
            "<foreach item='id' collection='strategyIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND tl.trade_type = 1 " + // 买入
            "AND tl.is_delete = 0 " +
            "AND gs.is_delete = 0 " +
            "GROUP BY gs.id</script>")
    @Results({
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "totalInvestment", column = "total_investment")
    })
    List<Map<String, Object>> getTotalInvestments(@Param("strategyIds") List<Long> strategyIds);

    @Select("<script>" +
            "SELECT gs.id as strategy_id, " +
            "COUNT(DISTINCT tl.grid_id) as sell_count " +
            "FROM grid_strategy gs " +
            "INNER JOIN trade_log tl ON tl.strategy_id = gs.id " +
            "WHERE gs.id IN " +
            "<foreach item='id' collection='strategyIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND tl.trade_type = 2 " + // 卖出
            "AND tl.is_delete = 0 " +
            "AND gs.is_delete = 0 " +
            "GROUP BY gs.id</script>")
    @Results({
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "sellCount", column = "sell_count")
    })
    List<Map<String, Object>> getGridSellCounts(@Param("strategyIds") List<Long> strategyIds);

    @Select("<script>" +
            "SELECT gs.id as strategy_id, " +
            "SUM(tl.profit) as total_profit " +
            "FROM grid_strategy gs " +
            "INNER JOIN trade_log tl ON tl.strategy_id = gs.id " +
            "WHERE gs.id IN " +
            "<foreach item='id' collection='strategyIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND tl.trade_type = 2 " + // 卖出
            "AND tl.is_delete = 0 " +
            "AND gs.is_delete = 0 " +
            "GROUP BY gs.id</script>")
    @Results({
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "totalProfit", column = "total_profit")
    })
    List<Map<String, Object>> getTotalProfits(@Param("strategyIds") List<Long> strategyIds);

    @Select("<script>" +
            "SELECT gs.id as strategy_id, " +
            "SUM(g.hold_shares) as total_holding_shares " +
            "FROM grid_strategy gs " +
            "INNER JOIN grids g ON g.strategy_id = gs.id " +
            "WHERE gs.id IN " +
            "<foreach item='id' collection='strategyIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND g.status = 1 " +
            "AND g.is_delete = 0 " +
            "AND gs.is_delete = 0 " +
            "GROUP BY gs.id</script>")
    @Results({
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "totalHoldingShares", column = "total_holding_shares")
    })
    List<Map<String, Object>> getTotalHoldingShares(@Param("strategyIds") List<Long> strategyIds);

    @Select("<script>" +
            "SELECT gs.id as strategy_id, " +
            "SUM(tl.reminder_shares) as retain_profit_shares " +
            "FROM grid_strategy gs " +
            "INNER JOIN trade_log tl ON tl.strategy_id = gs.id " +
            "WHERE gs.id IN " +
            "<foreach item='id' collection='strategyIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND tl.trade_type = 2 " + // 卖出
            "AND tl.is_delete = 0 " +
            "AND gs.is_delete = 0 " +
            "GROUP BY gs.id</script>")
    @Results({
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "retainProfitShares", column = "retain_profit_shares")
    })
    List<Map<String, Object>> getRetainProfitShares(@Param("strategyIds") List<Long> strategyIds);
}
