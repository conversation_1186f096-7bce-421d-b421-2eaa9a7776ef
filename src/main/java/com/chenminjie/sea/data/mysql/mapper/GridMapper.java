package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.GridEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @File: GridMapper
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/09 23:30
 **/
@Mapper
public interface GridMapper {

    @Insert("insert into grid (strategy_id, grid_type, level, hold_shares," +
            "  theoretical_buy_price, theoretical_buy_shares, theoretical_sell_price, theoretical_sell_shares," +
            "  trigger_amount,  buy_price, status, open_id, buy_at)" +
            "  values (#{strategyId}, #{gridType}, #{level}, #{holdShares}," +
            "  #{theoreticalBuyPrice}, #{theoreticalBuyShares}, #{theoreticalSellPrice}, #{theoreticalSellShares}," +
            "  #{triggerAmount}, #{buyPrice}, #{status}, #{openId}, #{buyAt})")
    @Options(useGeneratedKeys=true, keyProperty="id")
    int insert(GridEntity gridEntity);

    @Select("select * from grid where open_id = #{openId} and strategy_id = #{strategyId} and is_delete = 0")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "holdShares", column = "hold_shares"),
            @Result(property = "theoreticalBuyPrice", column = "theoretical_buy_price"),
            @Result(property = "theoreticalBuyShares", column = "theoretical_buy_shares"),
            @Result(property = "theoreticalSellPrice", column = "theoretical_sell_price"),
            @Result(property = "theoreticalSellShares", column = "theoretical_sell_shares"),
            @Result(property = "triggerAmount", column = "trigger_amount"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "status", column = "status"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
            @Result(property = "buyAt", column = "buy_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    List<GridEntity> selectByOpenIdAndStrategyId(String openId, Long strategyId);

    @Select("select * from grid where open_id = #{openId} and strategy_id = #{strategyId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "holdShares", column = "hold_shares"),
            @Result(property = "theoreticalBuyPrice", column = "theoretical_buy_price"),
            @Result(property = "theoreticalBuyShares", column = "theoretical_buy_shares"),
            @Result(property = "theoreticalSellPrice", column = "theoretical_sell_price"),
            @Result(property = "theoreticalSellShares", column = "theoretical_sell_shares"),
            @Result(property = "triggerAmount", column = "trigger_amount"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "status", column = "status"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
            @Result(property = "buyAt", column = "buy_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    List<GridEntity> findAll(String openId, Long strategyId);

    @Select("<script>select * from grid where open_id = #{openId} and id in <foreach item='id' collection='gridIds' separator=',' open='(' close=')' >#{id}</foreach></script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "holdShares", column = "hold_shares"),
            @Result(property = "theoreticalBuyPrice", column = "theoretical_buy_price"),
            @Result(property = "theoreticalBuyShares", column = "theoretical_buy_shares"),
            @Result(property = "theoreticalSellPrice", column = "theoretical_sell_price"),
            @Result(property = "theoreticalSellShares", column = "theoretical_sell_shares"),
            @Result(property = "triggerAmount", column = "trigger_amount"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "status", column = "status"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
            @Result(property = "buyAt", column = "buy_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    List<GridEntity> findByOpenIdAndGridIds(String openId, Set<Long> gridIds);

    @Select("SELECT * FROM grid WHERE open_id = #{openId} AND strategy_id = #{strategyId}" +
            " AND level = #{level} AND grid_type = #{gridType} and is_delete = 0")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "holdShares", column = "hold_shares"),
            @Result(property = "theoreticalBuyPrice", column = "theoretical_buy_price"),
            @Result(property = "theoreticalBuyShares", column = "theoretical_buy_shares"),
            @Result(property = "theoreticalSellPrice", column = "theoretical_sell_price"),
            @Result(property = "theoreticalSellShares", column = "theoretical_sell_shares"),
            @Result(property = "triggerAmount", column = "trigger_amount"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "status", column = "status"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
            @Result(property = "buyAt", column = "buy_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    GridEntity getGrid(String openId, Long strategyId, Integer level, Integer gridType);

    @Select("SELECT * FROM grid WHERE id = #{id} for update")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "strategyId", column = "strategy_id"),
            @Result(property = "gridType", column = "grid_type"),
            @Result(property = "level", column = "level"),
            @Result(property = "holdShares", column = "hold_shares"),
            @Result(property = "theoreticalBuyPrice", column = "theoretical_buy_price"),
            @Result(property = "theoreticalBuyShares", column = "theoretical_buy_shares"),
            @Result(property = "theoreticalSellPrice", column = "theoretical_sell_price"),
            @Result(property = "theoreticalSellShares", column = "theoretical_sell_shares"),
            @Result(property = "triggerAmount", column = "trigger_amount"),
            @Result(property = "buyPrice", column = "buy_price"),
            @Result(property = "status", column = "status"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
            @Result(property = "buyAt", column = "buy_at"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "isDelete", column = "is_delete")
    })
    GridEntity getGridForUpdate(Long id);

    @Select("select count(*) from grid where open_id = #{openId} and strategy_id = #{strategyId} and is_delete = 0")
    int getValidGridCount(String openId, Long strategyId);

    @Update("update grid set hold_shares = #{holdShares}, status = #{status}, is_delete = #{isDelete}," +
            " update_at = #{updateAt} where id = #{id} and open_id = #{openId}")
    int sellUpdate(GridEntity entity);

    @Update("update grid set is_delete = true," +
            " update_at = #{updateAt} where strategy_id = #{strategyId} and open_id = #{openId}")
    int deleteByStrategyId(Long strategyId, String openId, Date updateAt);

    @Update("update grid set is_delete = true," +
            " update_at = #{updateAt} where id = #{id} and open_id = #{openId}")
    int deleteById(Long id, String openId, Date updateAt);

    @Update("update grid set hold_shares = #{holdShares}, buy_price = #{buyPrice}, buy_at = #{buyAt}," +
            " update_at = #{updateAt} where id = #{id} and open_id = #{openId}")
    void update(GridEntity gridEntity);

    @Update("update grid set trigger_amount = #{triggerAmount}, update_at = #{updateAt} where id = #{gridId} and open_id = #{openId}")
    int setTriggerAmount(Long gridId, String openId, Integer triggerAmount, Date updateAt);
}
