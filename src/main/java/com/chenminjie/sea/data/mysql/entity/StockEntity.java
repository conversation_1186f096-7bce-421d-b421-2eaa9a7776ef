package com.chenminjie.sea.data.mysql.entity;

import com.chenminjie.sea.domain.model.StockModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * @File: StockEntity.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/13 08:22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockEntity {

    private Long id;

    private String code;

    private String name;

    /**
     * 0: stock
     * 1: fund
     * 2: index
     */
    private Integer type;

    private String market;

    private String maxValue;

    private String minValue;

    public static StockEntity from(StockModel model) {
        StockEntity entity = new StockEntity();
        BeanUtils.copyProperties(model, entity);
        return entity;
    }

    public static StockModel toModel(StockEntity entity) {
        StockModel model = new StockModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }
}
