package com.chenminjie.sea.data.mysql.entity;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockIndexEntity {
    private Long id;
    private String code;
    private String name;
    private Date publishDate; // 发布时间
    private BigDecimal changeRate; // 涨跌幅
    private BigDecimal closePoint; // 收盘点位
    private Integer periodYear; // 数据周期年份
    private String metricType; // 指标类型
    private BigDecimal peTtm; // PE-TTM(当前值)
    private BigDecimal peTtmPercentile; // PE-TTM(分位点%)
    private BigDecimal pb; // PB(当前值)
    private BigDecimal pbPercentile; // PB(分位点%)
    private BigDecimal psTtm; // PS-TTM(当前值)
    private BigDecimal psTtmPercentile; // PS-TTM(分位点%)
    private BigDecimal dividendYield; // 股息率
    private Date createTime;
    private Date updateTime;
}
