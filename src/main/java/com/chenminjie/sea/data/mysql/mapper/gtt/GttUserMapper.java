package com.chenminjie.sea.data.mysql.mapper.gtt;

import com.chenminjie.sea.data.mysql.entity.gtt.GttUserEntity;
import org.apache.ibatis.annotations.*;

@Mapper
public interface GttUserMapper {

    @Insert("insert into gtt_user (apple_id, full_name, email) values (#{appleId}, #{fullName}, #{email})")
    @Options(useGeneratedKeys=true, keyProperty="id")
    int insert(GttUserEntity gttUserEntity);

    @Select("select * from gtt_user where apple_id = #{appleId}")
    @Results(value = {
            @Result(property = "appleId", column = "apple_id"),
            @Result(property = "fullName", column = "full_name"),
            @Result(property = "email", column = "email"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    GttUserEntity findByAppleId(String appleId);
}
