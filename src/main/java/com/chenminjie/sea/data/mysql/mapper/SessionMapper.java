package com.chenminjie.sea.data.mysql.mapper;

import com.chenminjie.sea.data.mysql.entity.SessionEntity;
import org.apache.ibatis.annotations.*;

/**
 * @File: SessionMapper
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/21 00:26
 **/
@Mapper
public interface SessionMapper {

    @Insert("insert into session (session_key, open_id, union_id) values(#{sessionKey}, #{openId}, #{unionId})")
    void createSession(SessionEntity sessionEntity);

    @Select("SELECT * FROM session WHERE session_key = #{sessionKey}")
    @Results({
            @Result(property = "sessionKey", column = "session_key"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "unionId", column = "union_id"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    SessionEntity findBySessionKey(String sessionKey);

    @Select("SELECT * FROM session WHERE open_id = #{openId} limit 1")
    @Results({
            @Result(property = "sessionKey", column = "session_key"),
            @Result(property = "openId", column = "open_id"),
            @Result(property = "unionId", column = "union_id"),
            @Result(property = "createAt", column = "create_at"),
            @Result(property = "updateAt", column = "update_at"),
    })
    SessionEntity findByOpenId(String openId);
}
