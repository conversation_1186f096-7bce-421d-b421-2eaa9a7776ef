package com.chenminjie.sea.data.gateway.financial;

import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.domain.core.financial.FinancialService;
import com.chenminjie.sea.domain.core.financial.dto.QueryFundsResponse;
import com.chenminjie.sea.domain.core.financial.dto.QueryIndexsResponse;
import com.chenminjie.sea.domain.core.financial.dto.QueryPriceResponse;
import com.chenminjie.sea.domain.core.financial.dto.QueryRequest;
import com.chenminjie.sea.domain.core.financial.dto.QueryStocksResponse;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.concurrent.*;

/**
 * @File: FinancialServiceImpl.java
 * @author: ChenMinJie
 * @create: 2023/09/12 22:34
 **/
@Slf4j
@Component
public class FinancialServiceImpl implements FinancialService {

    @Value("${financial.api.token}")
    private String TOKEN;

    private final RestTemplate restTemplate;

    private static final ExecutorService executor = new ThreadPoolExecutor(5, 20, 10,
            TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000));

    private static final Cache<String, QueryPriceResponse> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)  // 设置数据写入后一分钟过期
            .build();

    public FinancialServiceImpl() {
        this.restTemplate = new RestTemplate();
        // List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        // //Add the Jackson Message converter
        // MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();

        // // Note: here we are making this converter to process any kind of response,
        // // not only application/*json, which is the default behaviour
        // converter.setSupportedMediaTypes(Collections.singletonList(MediaType.ALL));
        // messageConverters.add(converter);
        // restTemplate.setMessageConverters(messageConverters);
    }

    @Override
    public QueryFundsResponse queryFunds(QueryRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.set("token", TOKEN);

            // 创建HttpEntity并设置header
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送GET请求并添加header
            ResponseEntity<QueryFundsResponse> response = restTemplate.exchange(
                    GET_FUNDS_API_URL,
                    HttpMethod.GET,
                    entity,
                    QueryFundsResponse.class
            );
            return response.getBody();
        } catch (RestClientException e) {
            log.error("request FinancialService queryFunds failed.", e);
            throw new InternalException(CommonErrorCode.FINANCIAL_SERVER_ERROR, "服务器繁忙,请稍侯重试", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public QueryStocksResponse queryStocks(QueryRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.set("token", TOKEN);

            // 创建HttpEntity并设置header
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送GET请求并添加header
            ResponseEntity<QueryStocksResponse> response = restTemplate.exchange(
                    GET_STOCKS_API_URL,
                    HttpMethod.GET,
                    entity,
                    QueryStocksResponse.class
            );
            return response.getBody();
        } catch (RestClientException e) {
            log.error("request FinancialService queryStocks failed.", e);
            throw new InternalException(CommonErrorCode.FINANCIAL_SERVER_ERROR, "服务器繁忙,请稍侯重试", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public QueryIndexsResponse queryIndexs(QueryRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.set("token", TOKEN);

            // 创建HttpEntity并设置header
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送GET请求并添加header
            ResponseEntity<QueryIndexsResponse> response = restTemplate.exchange(
                    GET_INDEXES_API_URL,
                    HttpMethod.GET,
                    entity,
                    QueryIndexsResponse.class
            );
            return response.getBody();
        } catch (RestClientException e) {
            log.error("request FinancialService query indexs failed.", e);
            throw new InternalException(CommonErrorCode.FINANCIAL_SERVER_ERROR, "服务器繁忙,请稍侯重试", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public QueryPriceResponse queryPrice(String code) {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(GET_PRICE_API_URL)
                .queryParam("code", code);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.set("token", TOKEN);

            // 创建HttpEntity并设置header
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送GET请求并添加header
            ResponseEntity<QueryPriceResponse> response = restTemplate.exchange(
                    uriComponentsBuilder.toUriString(),
                    HttpMethod.GET,
                    entity,
                    QueryPriceResponse.class
            );
            QueryPriceResponse queryPriceResponse = response.getBody();
            if (!Objects.isNull(queryPriceResponse)) {
                queryPriceResponse.getData().setCode(code);
            }
            return queryPriceResponse;
        } catch (RestClientException e) {
            log.error("request FinancialService queryPrice failed.", e);
            throw new InternalException(CommonErrorCode.FINANCIAL_SERVER_ERROR, "服务器繁忙,请稍侯重试", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public Set<QueryPriceResponse> queryStocksPrice(Set<String> codes) {
        CopyOnWriteArraySet<QueryPriceResponse> result = new CopyOnWriteArraySet<>();
        Set<String> candidates = new HashSet<>();
        for (String code : codes) {
            QueryPriceResponse res = cache.getIfPresent(code);
            if (res == null) {
                candidates.add(code);
            } else {
                result.add(res);
            }
        }
        log.info("cache hit: {}. latch size: {}", result.size(), candidates.size());
        final CountDownLatch countDownLatch = new CountDownLatch(candidates.size());

        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        headers.set("token", TOKEN);
        headers.set("Content-Type", "application/json");
        // 创建HttpEntity并设置header
        HttpEntity<String> entity = new HttpEntity<>(headers);

        for (String code : candidates) {
            executor.submit(() -> {
                try {
                    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(GET_PRICE_API_URL)
                            .queryParam("code", code);
                    ResponseEntity<QueryPriceResponse> response = restTemplate.exchange(
                            uriComponentsBuilder.toUriString(),
                            HttpMethod.GET,
                            entity,
                            QueryPriceResponse.class
                    );
                    QueryPriceResponse queryPriceResponse = response.getBody();
                    if (Objects.nonNull(queryPriceResponse)) {
                        queryPriceResponse.getData().setCode(code);
                        cache.put(code, response.getBody());
                        result.add(response.getBody());
                    }
                } catch (Exception e) {
                    log.info("something wrong: " + e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            log.info("start wait latch");
            countDownLatch.await(5, TimeUnit.SECONDS);
            log.info("end wait latch");
            return result;
        } catch (InterruptedException e) {
            throw new InternalException(CommonErrorCode.FINANCIAL_SERVER_ERROR, "服务器繁忙,请稍侯重试", new RuntimeException(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
