package com.chenminjie.sea.data.gateway.wechat;

import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.domain.core.session.WeChatSessionService;
import com.chenminjie.sea.domain.core.session.dto.Code2SessionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @File: SessionClient.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/16 23:17
 **/
@Slf4j
@Component
public class WeChatSessionServiceImpl implements WeChatSessionService {

    private final static String CODE_2_SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={code}&grant_type=authorization_code";

    @Value("${wechat.grid.appid}")
    private String GRID_APP_ID;

    // todo read from file
    @Value("${wechat.grid.secret}")
    private String GRID_APP_SECRET;

    private final RestTemplate restTemplate;

    public WeChatSessionServiceImpl() {
        this.restTemplate = new RestTemplate();
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        //Add the Jackson Message converter
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();

        // Note: here we are making this converter to process any kind of response,
        // not only application/*json, which is the default behaviour
        converter.setSupportedMediaTypes(Collections.singletonList(MediaType.ALL));
        messageConverters.add(converter);
        restTemplate.setMessageConverters(messageConverters);
    }

    @Override
    public Code2SessionResponse code2Session(String code) {
        try {
            return this.restTemplate.getForObject(CODE_2_SESSION_URL, Code2SessionResponse.class, GRID_APP_ID, GRID_APP_SECRET, code);
        } catch (RestClientException e) {
            log.error("request WeChat code2session failed.", e);
            throw new InternalException(CommonErrorCode.WX_SERVER_ERROR, "服务器繁忙,请稍侯重试", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
