package com.chenminjie.sea.data.gateway.apple;

import com.chenminjie.sea.domain.core.apple.AppleService;
import com.chenminjie.sea.domain.core.apple.dto.ApplePublicKeysResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 苹果外部服务实现
 */
@Slf4j
@Service
public class AppleServiceImpl implements AppleService {
    
    /**
     * 苹果公钥API地址
     */
    private static final String APPLE_PUBLIC_KEYS_URL = "https://appleid.apple.com/auth/keys";
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Override
    public ApplePublicKeysResponse getPublicKeys() {
        try {
            log.info("开始获取苹果公钥，URL: {}", APPLE_PUBLIC_KEYS_URL);
            ApplePublicKeysResponse response = restTemplate.getForObject(
                APPLE_PUBLIC_KEYS_URL, 
                ApplePublicKeysResponse.class
            );
            
            if (response != null && response.getKeys() != null && !response.getKeys().isEmpty()) {
                log.info("成功获取苹果公钥，公钥数量: {}", response.getKeys().size());
                return response;
            } else {
                log.warn("获取到的苹果公钥响应为空或无有效公钥");
                return null;
            }
        } catch (Exception e) {
            log.error("获取苹果公钥失败", e);
            return null;
        }
    }
} 