package com.chenminjie.sea.data.service.gtt;

import com.chenminjie.sea.data.mysql.entity.gtt.GttSessionEntity;
import com.chenminjie.sea.data.mysql.mapper.gtt.GttSessionMapper;
import com.chenminjie.sea.domain.core.gtt.GttSessionDataService;
import com.chenminjie.sea.domain.model.gtt.GttSessionModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class GttSessionDataServiceImpl implements GttSessionDataService {
    @Autowired
    private GttSessionMapper gttSessionMapper;

    @Override
    public GttSessionModel createSession(String sessionKey, String userId, Date expireAt) {
        GttSessionEntity entity = GttSessionEntity.builder()
                .sessionKey(sessionKey)
                .userId(userId)
                .isValid(true)
                .expireAt(expireAt)
                .build();
        gttSessionMapper.insert(
                GttSessionEntity.builder()
                        .sessionKey(sessionKey)
                        .userId(userId)
                        .isValid(true)
                        .expireAt(expireAt)
                        .build());

        GttSessionModel model = new GttSessionModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    @Override
    public GttSessionModel getSession(String sessionKey) {
        GttSessionEntity entity = gttSessionMapper.findBySessionKey(sessionKey);
        if (entity == null) {
            return null;
        }
        GttSessionModel model = new GttSessionModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }
}
