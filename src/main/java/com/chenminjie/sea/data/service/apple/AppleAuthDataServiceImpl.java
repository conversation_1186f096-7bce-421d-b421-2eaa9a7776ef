package com.chenminjie.sea.data.service.apple;

import com.chenminjie.sea.common.util.ConvertUtil;
import com.chenminjie.sea.data.mysql.entity.apple.AppleUserEntity;
import com.chenminjie.sea.data.mysql.mapper.apple.AppleUserMapper;
import com.chenminjie.sea.domain.core.apple.AppleAuthDataService;
import com.chenminjie.sea.domain.model.apple.AppleUserModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 苹果授权数据服务实现
 */
@Service
public class AppleAuthDataServiceImpl implements AppleAuthDataService {
    
    @Autowired
    private AppleUserMapper appleUserMapper;
    
    @Override
    public AppleUserModel findByAppleUserId(String appleUserId) {
        AppleUserEntity entity = appleUserMapper.findByAppleUserId(appleUserId);
        return entity != null ? ConvertUtil.convert(entity, AppleUserModel.class) : null;
    }
    
    @Override
    public AppleUserModel findByEmail(String email) {
        AppleUserEntity entity = appleUserMapper.findByEmail(email);
        return entity != null ? ConvertUtil.convert(entity, AppleUserModel.class) : null;
    }
    
    @Override
    public AppleUserModel createUser(AppleUserModel userModel) {
        AppleUserEntity entity = ConvertUtil.convert(userModel, AppleUserEntity.class);
        appleUserMapper.insert(entity);
        return ConvertUtil.convert(entity, AppleUserModel.class);
    }
    
    @Override
    public AppleUserModel updateUser(AppleUserModel userModel) {
        AppleUserEntity entity = ConvertUtil.convert(userModel, AppleUserEntity.class);
        appleUserMapper.updateByAppleUserId(entity);
        // 重新查询返回最新数据
        AppleUserEntity updatedEntity = appleUserMapper.findByAppleUserId(userModel.getAppleUserId());
        return updatedEntity != null ? ConvertUtil.convert(updatedEntity, AppleUserModel.class) : null;
    }
    
    @Override
    public AppleUserModel findById(Long id) {
        AppleUserEntity entity = appleUserMapper.findById(id);
        return entity != null ? ConvertUtil.convert(entity, AppleUserModel.class) : null;
    }
} 