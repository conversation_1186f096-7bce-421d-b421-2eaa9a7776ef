package com.chenminjie.sea.data.service;

import com.chenminjie.sea.data.mysql.entity.GridEntity;
import com.chenminjie.sea.data.mysql.mapper.GridMapper;
import com.chenminjie.sea.domain.core.grid.GridDataService;
import com.chenminjie.sea.domain.model.GridModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @File: GridDataServiceImpl.java
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2023/08/12 17:39
 **/
@Component
public class GridDataServiceImpl implements GridDataService {

    @Autowired
    private GridMapper gridMapper;

    @Override
    public GridModel createGrid(GridModel gridModel) {
        GridEntity gridEntity = new GridEntity();
        BeanUtils.copyProperties(gridModel, gridEntity);
        gridMapper.insert(gridEntity);
        GridModel grid = new GridModel();
        BeanUtils.copyProperties(gridEntity, grid);
        return grid;
    }

    @Override
    public void sellUpdateGrid(GridModel gridModel) {
        GridEntity gridEntity = new GridEntity();
        BeanUtils.copyProperties(gridModel, gridEntity);
        gridMapper.sellUpdate(gridEntity);
    }

    @Override
    public List<GridModel> getGrids(String openId, Long strategyId) {
        List<GridEntity> gridEntities = gridMapper.selectByOpenIdAndStrategyId(openId, strategyId);
        List<GridModel> res = new ArrayList<>(gridEntities.size());
        for (GridEntity gridEntity : gridEntities) {
            GridModel model = new GridModel();
            BeanUtils.copyProperties(gridEntity, model);
            res.add(model);
        }
        return res;
    }

    @Override
    public List<GridModel> getAllGrids(String openId, Long strategyId) {
        List<GridEntity> gridEntities = gridMapper.findAll(openId, strategyId);
        List<GridModel> res = new ArrayList<>(gridEntities.size());
        for (GridEntity gridEntity : gridEntities) {
            GridModel model = new GridModel();
            BeanUtils.copyProperties(gridEntity, model);
            res.add(model);
        }
        return res;
    }

    @Override
    public List<GridModel> getGrids(String openId, Set<Long> gridIds) {
        List<GridEntity> gridEntities = gridMapper.findByOpenIdAndGridIds(openId, gridIds);
        List<GridModel> res = new ArrayList<>(gridEntities.size());
        for (GridEntity gridEntity : gridEntities) {
            GridModel model = new GridModel();
            BeanUtils.copyProperties(gridEntity, model);
            res.add(model);
        }
        return res;
    }

    @Override
    public GridModel getGrid(String openId, Long strategyId, Integer level, Integer gridType) {
        GridEntity entity = gridMapper.getGrid(openId, strategyId, level, gridType);
        if (Objects.isNull(entity)) {
            return null;
        }
        GridModel model = new GridModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    @Override
    public GridModel getGrid(Long gridId) {
        GridEntity entity = gridMapper.getGridForUpdate(gridId);
        GridModel model = new GridModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    @Override
    public int getValidGridCount(String openId, Long strategyId) {
        return gridMapper.getValidGridCount(openId, strategyId);
    }

    @Override
    public int deleteByStrategyId(Long strategyId, String openId) {
        return gridMapper.deleteByStrategyId(strategyId, openId, new Date());
    }

    @Override
    public int deleteById(Long gridId, String openId) {
        return gridMapper.deleteById(gridId, openId, new Date());
    }

    @Override
    public void updateGrid(GridModel gridModel) {
        GridEntity entity = new GridEntity();
        BeanUtils.copyProperties(gridModel, entity);
        gridMapper.update(entity);
    }

    @Override
    public int setGridTriggerAmount(Long girdId, String openId, Integer triggerAmount) {
        return gridMapper.setTriggerAmount(girdId, openId, triggerAmount, new Date());
    }
}
