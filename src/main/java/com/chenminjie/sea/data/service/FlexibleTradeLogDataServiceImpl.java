package com.chenminjie.sea.data.service;

import com.chenminjie.sea.data.mysql.entity.FlexibleTradeLogEntity;
import com.chenminjie.sea.data.mysql.mapper.FlexibleTradeLogMapper;
import com.chenminjie.sea.domain.core.strategy.FlexibleTradeLogDataService;
import com.chenminjie.sea.domain.model.FlexibleTradeLogModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class FlexibleTradeLogDataServiceImpl implements FlexibleTradeLogDataService {

    @Autowired
    private FlexibleTradeLogMapper flexibleTradeLogMapper;

    @Override
    public List<FlexibleTradeLogModel> getLogsByStrategyIdAndOpenId(Long strategyId, String openId) {
        List<FlexibleTradeLogModel> flexibleTradeLogModels = new ArrayList<>();
        List<FlexibleTradeLogEntity> flexibleTradeLogEntities = flexibleTradeLogMapper.findByStrategyIdAndOpenId(strategyId, openId);
        flexibleTradeLogEntities.forEach(x -> {
            FlexibleTradeLogModel model = new FlexibleTradeLogModel();
            BeanUtils.copyProperties(x, model);
            flexibleTradeLogModels.add(model);
        });
        return flexibleTradeLogModels;
    }

    @Override
    public void createFlexibleTradeLog(FlexibleTradeLogModel flexibleTradeLogModel) {
        FlexibleTradeLogEntity entity = new FlexibleTradeLogEntity();
        BeanUtils.copyProperties(flexibleTradeLogModel, entity);
        flexibleTradeLogMapper.insert(entity);
    }
}
