package com.chenminjie.sea.data.service;

import com.chenminjie.sea.common.util.ConvertUtil;
import com.chenminjie.sea.data.mysql.entity.TradeLogEntity;
import com.chenminjie.sea.data.mysql.mapper.TradeLogMapper;
import com.chenminjie.sea.domain.core.grid.TradeLogDataService;
import com.chenminjie.sea.domain.model.TradeLogModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @File: TradeLogDataServiceImpl.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/08/23 20:34
 **/
@Component
public class TradeLogDataServiceImpl implements TradeLogDataService {

    @Autowired
    private TradeLogMapper tradeLogMapper;

    @Override
    public Long create(TradeLogModel model) {
        TradeLogEntity entity = new TradeLogEntity();
        BeanUtils.copyProperties(model, entity);
        return tradeLogMapper.insert(entity);
    }

    @Override
    public List<TradeLogModel> getTradeLogByGridId(String openId, Long gridId) {
        List<TradeLogEntity> entities = tradeLogMapper.findByOpenIdAndGridId(openId, gridId);
        List<TradeLogModel> res = new ArrayList<>(entities.size());
        for (TradeLogEntity entity : entities) {
            TradeLogModel model = new TradeLogModel();
            BeanUtils.copyProperties(entity, model);
            res.add(model);
        }
        return res;
    }

    @Override
    public TradeLogModel getTradeLog(String openId, Long id) {
        TradeLogEntity entity = tradeLogMapper.findById(openId, id);
        if (Objects.isNull(entity)) {
            return null;
        }
        return ConvertUtil.convert(entity, TradeLogModel.class);
    }

    @Override
    public List<TradeLogModel> getTradeLogByStrategyId(String openId, Long strategyId) {
        List<TradeLogEntity> entities = tradeLogMapper.findByOpenIdAndStrategyId(openId, strategyId);
        List<TradeLogModel> res = new ArrayList<>(entities.size());
        for (TradeLogEntity entity : entities) {
            TradeLogModel model = new TradeLogModel();
            BeanUtils.copyProperties(entity, model);
            res.add(model);
        }
        return res;
    }

    @Override
    public int deleteByIdAndOpenId(Long id, String openId) {
        return tradeLogMapper.deleteByIdAndOpenId(id, openId);
    }

    @Override
    public int deleteByGridIdAndOpenId(Long gridId, String openId) {
        return tradeLogMapper.deleteByGridIdAndOpenId(gridId, openId);
    }

    @Override
    public void updateTradeLog(TradeLogModel model) {
        TradeLogEntity entity = ConvertUtil.convert(model, TradeLogEntity.class);
        tradeLogMapper.updateTradeLog(entity);
    }

    @Override
    public List<TradeLogModel> getTradeLogsByDateRange(String openId, Date startDate, Date endDate) {
        // 获取指定日期范围内的有效（未删除）交易日志
        List<TradeLogEntity> entities = tradeLogMapper.findByOpenIdAndDateRange(openId, startDate, endDate);
        
        // 使用stream进行批量转换
        return entities.stream()
                .filter(entity -> !Boolean.TRUE.equals(entity.getIsDelete())) // 过滤已删除的记录
                .map(entity -> {
                    TradeLogModel model = new TradeLogModel();
                    BeanUtils.copyProperties(entity, model);
                    return model;
                })
                .collect(Collectors.toList());
    }
}
