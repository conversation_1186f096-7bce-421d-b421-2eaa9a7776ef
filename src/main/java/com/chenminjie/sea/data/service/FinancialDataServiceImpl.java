package com.chenminjie.sea.data.service;

import com.chenminjie.sea.data.mysql.entity.StockEntity;
import com.chenminjie.sea.data.mysql.entity.StockIndexEntity;
import com.chenminjie.sea.data.mysql.mapper.StockIndexMapper;
import com.chenminjie.sea.data.mysql.mapper.StockMapper;
import com.chenminjie.sea.domain.core.financial.FinancialDataService;
import com.chenminjie.sea.domain.model.StockIndexModel;
import com.chenminjie.sea.domain.model.StockModel;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @File: FinancialDataServiceImpl.java
 * @author: ChenMinJie
 * @create: 2023/09/13 08:37
 **/
@Component
public class FinancialDataServiceImpl implements FinancialDataService {

    @Autowired
    private StockMapper stockMapper;

    @Autowired
    private StockIndexMapper stockIndexMapper;

    @Override
    public int batchUpsert(List<StockModel> stockModelList) {
        List<StockEntity> stockEntityList = stockModelList.stream().map(StockEntity::from).collect(Collectors.toList());
        return stockMapper.batchUpsert(stockEntityList);
    }

    @Override
    public List<StockModel> searchByCode(String codePrefix) {
        List<StockEntity> stockEntities = stockMapper.searchByCode(codePrefix + "%");
        return stockEntities.stream().map(StockEntity::toModel).collect(Collectors.toList());
    }

    @Override
    public List<StockModel> queryStocksByCodes(List<String> codes) {
        List<StockEntity> stockEntities = stockMapper.getStocksByCodes(codes);
        return stockEntities.stream().map(StockEntity::toModel).collect(Collectors.toList());
    }

    @Override
    public List<StockIndexModel> getIndexByCodes(List<String> codes, String metricType, Integer periodYear) {
        List<StockIndexEntity> stockIndexEntities = stockIndexMapper.getIndexByCodes(codes, metricType, periodYear);
        return stockIndexEntities.stream().map(entity -> {
            StockIndexModel model = new StockIndexModel();
            BeanUtils.copyProperties(entity, model);
            return model;
        }).collect(Collectors.toList());
    }

    @Override
    public void batchUpsertIndex(List<StockIndexModel> indexModels) {
        if (indexModels == null || indexModels.isEmpty()) {
            return;
        }
        List<StockIndexEntity> stockIndexEntities = indexModels.stream().map(model -> {
            StockIndexEntity stockIndexEntity = new StockIndexEntity();
            BeanUtils.copyProperties(model, stockIndexEntity);
            return stockIndexEntity;
        }).collect(Collectors.toList());
        // Changed from batchUpsert(stockIndexEntities) to batchUpsert(stockIndexEntities)
        stockIndexMapper.batchUpsert(stockIndexEntities);
    }

    @Override
    public List<StockIndexModel> searchStockIndex(String codePrefix, String metricType, Integer periodYear) {
        List<StockIndexEntity> stockIndexEntities = stockIndexMapper.searchStockIndex(codePrefix, metricType, periodYear);
        return stockIndexEntities.stream().map(entity -> {
            StockIndexModel model = new StockIndexModel();
            BeanUtils.copyProperties(entity, model);
            return model;
        }).collect(Collectors.toList());
    }

    @Override
    @Deprecated
    public List<StockIndexModel> filterByPercentile(String metricType, Double fiveYearPercentile, Double tenYearPercentile) {
        return filterByMultiplePercentiles(metricType, fiveYearPercentile, tenYearPercentile, null, null);
    }

    @Override
    public List<StockIndexModel> filterByMultiplePercentiles(
            String metricType,
            Double pePercentile5Y,
            Double pePercentile10Y,
            Double pbPercentile5Y,
            Double pbPercentile10Y) {
        List<StockIndexEntity> stockIndexEntities = stockIndexMapper.filterByMultiplePercentiles(
                metricType,
                pePercentile5Y,
                pePercentile10Y,
                pbPercentile5Y,
                pbPercentile10Y
        );
        
        return stockIndexEntities.stream().map(entity -> {
            StockIndexModel model = new StockIndexModel();
            BeanUtils.copyProperties(entity, model);
            return model;
        }).collect(Collectors.toList());
    }
}
