package com.chenminjie.sea.data.service;

import com.chenminjie.sea.common.error.CommonErrorCode;
import com.chenminjie.sea.common.error.ErrorBody;
import com.chenminjie.sea.common.exception.InternalException;
import com.chenminjie.sea.data.mysql.entity.GridStrategyEntity;
import com.chenminjie.sea.data.mysql.mapper.GridStrategyMapper;
import com.chenminjie.sea.domain.core.strategy.StrategyDataService;
import com.chenminjie.sea.domain.model.StrategyModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @File: StrategyDataServiceImpl.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/25 22:48
 * @update: 2025/03/02
 **/
@Slf4j
@Component
public class StrategyDataServiceImpl implements StrategyDataService {

    @Autowired
    private GridStrategyMapper gridStrategyMapper;

    @Override
    public List<StrategyModel> getStrategyByOpenId(String openId) {
        List<GridStrategyEntity> gridStrategyEntities = gridStrategyMapper.findByOpenId(openId);
        List<StrategyModel> strategyModels = new ArrayList<>(gridStrategyEntities.size());
        gridStrategyEntities.forEach(x -> {
            StrategyModel strategyModel = new StrategyModel();
            BeanUtils.copyProperties(x, strategyModel);
            strategyModels.add(strategyModel);
        });
        return strategyModels;
    }

    @Override
    public StrategyModel createStrategy(StrategyModel strategy) {
        GridStrategyEntity entity = new GridStrategyEntity();
        BeanUtils.copyProperties(strategy, entity);
        Long id = gridStrategyMapper.insert(entity);
        StrategyModel result = new StrategyModel();
        BeanUtils.copyProperties(strategy, result);
        result.setId(id);
        return result;
    }

    @Override
    public void deleteById(Long id, String openId) {
        gridStrategyMapper.delete(id, openId);
    }

    @Override
    public void addRemainderShares(String openId, Long strategyId, Integer addShares) {
        GridStrategyEntity entity = gridStrategyMapper.findByOpenIdAndStrategyId(openId, strategyId);
        if (Objects.nonNull(entity)) {
            entity.setRemainderShares(entity.getRemainderShares() + addShares);
            entity.setUpdateAt(new Date());
            gridStrategyMapper.updateRemainderShares(entity);
        } else {
            log.error("strategy not found, strategyId({}), openId({})", strategyId, openId);
            throw new InternalException(ErrorBody.builder()
                    .code(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorCode())
                    .reason(CommonErrorCode.ILLEGAL_ARGUMENTS.getErrorMsg())
                    .detail("策略不存在").build(), new IllegalArgumentException(), HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public StrategyModel getStrategyById(Long id, String openId) {
        GridStrategyEntity entity = gridStrategyMapper.findByOpenIdAndStrategyId(openId, id);
        if (Objects.nonNull(entity)) {
            StrategyModel model = new StrategyModel();
            BeanUtils.copyProperties(entity, model);
            return model;
        }
        return null;
    }

    @Override
    public StrategyModel queryForUpdate(Long id, String openId) {
        GridStrategyEntity entity = gridStrategyMapper.selectForUpdate(id, openId);
        if (Objects.nonNull(entity)) {
            StrategyModel model = new StrategyModel();
            BeanUtils.copyProperties(entity, model);
            return model;
        }
        return null;
    }

    @Override
    public int updateStrategy(StrategyModel strategyModel) {
        GridStrategyEntity entity = new GridStrategyEntity();
        BeanUtils.copyProperties(strategyModel, entity);
        entity.setUpdateAt(new Date());
        return gridStrategyMapper.update(entity);
    }

    @Override
    public Map<Long, Integer> getTotalInvestments(List<Long> strategyIds) {
        if (strategyIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return gridStrategyMapper.getTotalInvestments(strategyIds).stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("strategy_id"),
                            map -> ((Number) map.get("total_investment")).intValue(),
                            (a, b) -> a
                    ));
        } catch (Exception e) {
            log.error("Failed to get total investments for strategies: {}", strategyIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, Integer> getGridSellCounts(List<Long> strategyIds) {
        if (strategyIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return gridStrategyMapper.getGridSellCounts(strategyIds).stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("strategy_id"),
                            map -> ((Number) map.get("sell_count")).intValue(),
                            (a, b) -> a
                    ));
        } catch (Exception e) {
            log.error("Failed to get grid sell counts for strategies: {}", strategyIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, Integer> getTotalProfits(List<Long> strategyIds) {
        if (strategyIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return gridStrategyMapper.getTotalProfits(strategyIds).stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("strategy_id"),
                            map -> ((Number) map.get("total_profit")).intValue(),
                            (a, b) -> a
                    ));
        } catch (Exception e) {
            log.error("Failed to get total profits for strategies: {}", strategyIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, Integer> getTotalHoldingShares(List<Long> strategyIds) {
        if (strategyIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return gridStrategyMapper.getTotalHoldingShares(strategyIds).stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("strategy_id"),
                            map -> ((Number) map.get("total_holding_shares")).intValue(),
                            (a, b) -> a
                    ));
        } catch (Exception e) {
            log.error("Failed to get total holding shares for strategies: {}", strategyIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, Integer> getRetainProfitShares(List<Long> strategyIds) {
        if (strategyIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return gridStrategyMapper.getRetainProfitShares(strategyIds).stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("strategy_id"),
                            map -> ((Number) map.get("retain_profit_shares")).intValue(),
                            (a, b) -> a
                    ));
        } catch (Exception e) {
            log.error("Failed to get retain profit shares for strategies: {}", strategyIds, e);
            return Collections.emptyMap();
        }
    }
}
