package com.chenminjie.sea.data.service.gtt;

import com.chenminjie.sea.data.mysql.entity.gtt.GttUserEntity;
import com.chenminjie.sea.data.mysql.mapper.gtt.GttUserMapper;
import com.chenminjie.sea.domain.core.gtt.GttUserDataService;
import com.chenminjie.sea.domain.model.gtt.GttUserModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GttUserDataServiceImpl implements GttUserDataService {
    @Autowired
    private GttUserMapper gttUserMapper;

    @Override
    public int createUser(GttUserModel gttUserModel) {
        GttUserEntity entity = new GttUserEntity();
        BeanUtils.copyProperties(gttUserModel, entity);
        return gttUserMapper.insert(entity);
    }

    @Override
    public GttUserModel getUser(String appleId) {
        GttUserEntity entity = gttUserMapper.findByAppleId(appleId);
        if (entity == null) {
            return null;
        }
        GttUserModel model = new GttUserModel();
        BeanUtils.copyProperties(entity, model);
        return model;
    }
}
