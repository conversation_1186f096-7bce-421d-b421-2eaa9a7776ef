package com.chenminjie.sea.data.service;

import com.chenminjie.sea.data.mysql.entity.SessionEntity;
import com.chenminjie.sea.data.mysql.mapper.SessionMapper;
import com.chenminjie.sea.domain.core.session.SessionDataService;
import com.chenminjie.sea.domain.model.SessionModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @File: SessionDataServiceImpl.java
 * @author: <PERSON><PERSON>in<PERSON><PERSON>
 * @create: 2023/04/24 22:58
 **/
@Component
public class SessionDataServiceImpl implements SessionDataService {

    private final SessionMapper sessionMapper;

    @Autowired
    public SessionDataServiceImpl(SessionMapper sessionMapper) {
        this.sessionMapper = sessionMapper;
    }

    @Override
    public void createSession(SessionModel sessionModel) {
        SessionEntity sessionEntity = new SessionEntity();
        BeanUtils.copyProperties(sessionModel, sessionEntity);
        sessionMapper.createSession(sessionEntity);
    }

    @Override
    public SessionModel getSessionByOpenId(String openId) {
        SessionEntity sessionEntity = sessionMapper.findByOpenId(openId);
        if (sessionEntity != null) {
            SessionModel sessionModel = new SessionModel();
            BeanUtils.copyProperties(sessionEntity, sessionModel);
            return sessionModel;
        }
        return null;
    }

    @Override
    public SessionModel getSession(String sessionKey) {
        SessionEntity sessionEntity = sessionMapper.findBySessionKey(sessionKey);
        if (sessionEntity != null) {
            SessionModel sessionModel = new SessionModel();
            BeanUtils.copyProperties(sessionEntity, sessionModel);
            return sessionModel;
        }
        return null;
    }
}
