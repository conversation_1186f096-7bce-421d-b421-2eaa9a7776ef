---
description: 
globs: 
alwaysApply: true
---
# 技术栈概述

Sea项目使用以下主要技术和框架：

## 后端框架

- **Spring Boot** ([pom.xml](mdc:pom.xml))：主要应用框架，提供自动配置、依赖管理等功能
- **Spring MVC**：Web框架，处理HTTP请求
- **Spring AOP**：面向切面编程，用于横切关注点
- **Spring JDBC**：数据库访问

## 数据访问

- **MyBatis**：ORM框架，用于数据库操作
- **PageHelper**：MyBatis分页插件
- **MySQL**：主要关系型数据库
- **H2 Database**：内存数据库，用于测试

## 微信接入

- **weixin-java-miniapp**：微信小程序开发SDK

## 开发工具

- **Lombok**：自动生成getter/setter等样板代码
- **Spring Boot DevTools**：开发工具，支持热重载等

## 测试框架

- **JUnit**：单元测试框架
- **Spring Boot Test**：集成测试支持

## 构建工具

- **Maven**：项目管理和构建工具

## 编程语言

- **Java 8**：主要编程语言
