---
description: 
globs: 
alwaysApply: false
---
# 测试指南

Sea项目采用测试驱动开发(TDD)方法，确保代码质量和可维护性。

## 测试类型

1. **单元测试**
   - 测试范围：单个类或方法
   - 框架：JUnit、Mockito
   - 目录：[src/test/java](mdc:src/test/java)
   - 命名规范：被测试类名+Test（例如：UserServiceTest）

2. **集成测试**
   - 测试范围：多个组件的交互
   - 框架：Spring Boot Test
   - 注解：@SpringBootTest

3. **数据库测试**
   - 使用H2内存数据库
   - 使用测试配置文件

## 测试编写准则

### 单元测试结构（Given-When-Then）

```java
@Test
public void shouldDoSomething() {
    // Given - 设置测试前提条件
    User user = new User("username", "password");
    when(userRepository.findByUsername("username")).thenReturn(user);

    // When - 执行被测试方法
    boolean result = userService.authenticate("username", "password");

    // Then - 验证结果
    assertTrue(result);
    verify(userRepository).findByUsername("username");
}
```

### 模拟外部依赖

- 使用Mockito模拟依赖注入的服务
- 对于领域服务，模拟仓储接口
- 对于应用服务，模拟领域服务

### 测试覆盖率要求

- 核心业务逻辑（领域层）：>90%
- 应用服务层：>80%
- 数据访问层：重点测试复杂查询
- 控制器层：验证请求映射和参数绑定

## 最佳实践

- 保持测试独立，避免测试间的依赖
- 测试应该快速运行
- 测试不应依赖外部资源（如实际数据库、网络连接）
- 使用固定的测试数据，避免随机值
- 为边界条件和异常情况编写测试
