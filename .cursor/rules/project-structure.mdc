---
description: 
globs: 
alwaysApply: true
---
# 项目结构概述

Sea项目是一个使用Spring Boot框架的Java应用程序，采用了清晰的分层架构设计。

## 主要目录结构

- [src/main/java/com/chenminjie/sea](mdc:src/main/java/com/chenminjie/sea)：主要代码目录
  - [SeaApplication.java](mdc:src/main/java/com/chenminjie/sea/SeaApplication.java)：应用程序入口点
  - [presentation](mdc:src/main/java/com/chenminjie/sea/presentation)：表示层，包含控制器
  - [application](mdc:src/main/java/com/chenminjie/sea/application)：应用层，协调业务流程
  - [domain](mdc:src/main/java/com/chenminjie/sea/domain)：领域层，包含核心业务逻辑
  - [data](mdc:src/main/java/com/chenminjie/sea/data)：数据层，处理数据持久化
  - [common](mdc:src/main/java/com/chenminjie/sea/common)：通用组件和工具

- [src/main/resources](mdc:src/main/resources)：配置文件和静态资源
- [src/test](mdc:src/test)：测试代码

## 主要配置文件

- [pom.xml](mdc:pom.xml)：Maven项目配置和依赖管理
