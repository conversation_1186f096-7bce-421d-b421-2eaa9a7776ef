---
description: 
globs: 
alwaysApply: false
---
# 开发指南

## 测试驱动开发 (TDD)

- 编写单元测试时应遵循 given-when-then 三段式：
  - given：给出测试的初始条件和状态
  - when：执行要测试的代码
  - then：验证结果
- 使用 Mockito 模拟外部依赖
- 保持测试的独立性，即使在断网环境下也能运行

## 代码结构规范

- 保持类的单一职责
- 控制器层只处理请求/响应，不包含业务逻辑
- 应用服务层协调业务流程，但不包含业务规则
- 领域层包含核心业务逻辑和规则
- 数据访问层处理数据持久化，实现领域层定义的接口

## 编码风格

- 使用 Lombok 减少样板代码
- 遵循 Java 编码规范
- 方法和类应有明确的职责和适当的命名
- 注释应解释"为什么"而不仅仅是"做什么"
- 保持方法简短，一般不超过 30 行

## 错误处理

- 使用异常处理业务错误
- 全局异常处理器统一处理异常并返回适当的HTTP状态码
- 记录足够的日志信息以便调试

## 数据库访问

- 使用 MyBatis 进行数据库操作
- 复杂查询应使用XML配置而不是注解
- 使用 PageHelper 进行分页处理
