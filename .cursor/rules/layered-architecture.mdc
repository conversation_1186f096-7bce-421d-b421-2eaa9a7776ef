---
description: 
globs: 
alwaysApply: true
---
# 分层架构说明

Sea项目采用清晰的分层架构，遵循依赖倒置原则和关注点分离原则。

## 架构层次（从外到内）

1. **表示层 (Presentation Layer)**
   - 位置：[presentation](mdc:src/main/java/com/chenminjie/sea/presentation)
   - 职责：接收和响应用户请求，将结果返回给用户
   - 组件：控制器 (Controllers)
   - 依赖：仅依赖于应用层

2. **应用层 (Application Layer)**
   - 位置：[application](mdc:src/main/java/com/chenminjie/sea/application)
   - 职责：协调业务流程，管理事务，转换数据，但不包含业务规则
   - 组件：服务 (Services)、DTO、过滤器
   - 依赖：依赖于领域层

3. **领域层 (Domain Layer)**
   - 位置：[domain](mdc:src/main/java/com/chenminjie/sea/domain)
   - 职责：包含业务模型和核心业务逻辑
   - 组件：领域模型、领域服务、仓储接口
   - 依赖：不依赖其他层

4. **数据层 (Data Layer)**
   - 位置：[data](mdc:src/main/java/com/chenminjie/sea/data)
   - 职责：实现领域层定义的仓储接口，处理数据持久化
   - 组件：仓储实现、数据库访问对象、ORM映射
   - 依赖：依赖于领域层（实现接口）

## 依赖规则

- 内层不应该依赖外层
- 外层可以依赖内层
- 领域层是核心，不依赖于其他层
- 通过依赖倒置原则解决内层需要外层功能的问题
