---
description: 
globs: 
alwaysApply: true
---
# code guidelines
##开发规范
【代码生成原则（按优先级）】
1. First Principles（第一性原理）：梳理最核心需求与边界
2. YAGNI：只实现当前真正需要的功能
3. KISS：保持设计和实现的简单性
4. SOLID：面向对象/模块化设计时，遵循单一职责、开放封闭等
5. TDD：测试驱动开发，使用Junit和Mockito，单测的典型格式given-when-then三段式，given：给出测试的初始条件和状态，when: 执行要测试的代码，then: 验证结果，Mock掉外部依赖，保证单元测试的独立性，让单测即使在断网的环境依然可以正常运行
6. DRY：消除重复，提炼公用逻辑

## 根据场景动态调整顺序
- 架构级／需求分析（Project Kickoff） First Principles → YAGNI → KISS → SOLID → DRY → TDD
- 新功能迭代／增量开发：TDD → YAGNI → KISS → SOLID → DRY → First Principles
- 小函数／工具库实现：TDD → KISS → DRY → YAGNI → SOLID → First Principles
- 复杂业务组件／面向对象建模：First Principles → TDD → SOLID → YAGNI → KISS → DRY