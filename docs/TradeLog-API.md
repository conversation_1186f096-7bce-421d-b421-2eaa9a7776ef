# 获取交易日志接口文档

## 基本信息

- **接口URL**: `/api/v1/tradelogs`
- **请求方法**: GET
- **接口描述**: 根据日期参数查询交易日志

## 请求头

| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| GSA-Session | 是 | string | 用户会话信息 |

## 请求参数

| 参数名 | 必选 | 类型 | 取值范围 | 说明 |
|--------|------|------|----------|------|
| year | 否 | integer | [2000-2100] | 年份 |
| month | 否 | integer | [1-12] | 月份 |
| day | 否 | integer | [1-31] | 日期 |

### 参数组合规则
支持以下查询模式：
1. 不提供参数：返回所有日志
2. 仅提供year：返回指定年份的日志
3. 提供year和month：返回指定年月的日志
4. 提供year、month和day：返回指定日期的日志

**注意**：
- month参数必须和year一起使用
- day参数必须和year、month一起使用

## 响应参数

响应类型：`array<object>`

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | long | 交易日志ID |
| strategyId | long | 策略ID |
| gridId | long | 网格ID |
| gridType | integer | 网格类型 |
| level | integer | 网格等级 |
| tradeType | integer | 交易类型 |
| tradeShares | integer | 交易股数 |
| tradePrice | integer | 交易价格 |
| reminderShares | integer | 剩余股数 |
| theoreticalPrice | integer | 理论价格 |
| theoreticalShares | integer | 理论股数 |
| tradeAt | date | 交易时间 |
| createAt | date | 创建时间 |
| profit | integer | 盈利 |
| profitRatio | integer | 盈利比 |
| apy | integer | 年化利率 |
| holdTime | integer | 持有时间 |

## 请求示例

### 查询指定日期的交易日志
```http
GET /api/v1/tradelogs?year=2025&month=2&day=28
GSA-Session: xxx
```

### 查询指定年月的交易日志
```http
GET /api/v1/tradelogs?year=2025&month=2
GSA-Session: xxx
```

### 查询指定年份的交易日志
```http
GET /api/v1/tradelogs?year=2025
GSA-Session: xxx
```

### 查询所有交易日志
```http
GET /api/v1/tradelogs
GSA-Session: xxx
```

## 响应示例

```json
[
  {
    "id": 1,
    "strategyId": 100,
    "gridId": 1001,
    "gridType": 1,
    "level": 1,
    "tradeType": 1,
    "tradeShares": 100,
    "tradePrice": 1000,
    "reminderShares": 0,
    "theoreticalPrice": 1000,
    "theoreticalShares": 100,
    "tradeAt": "2025-02-28T10:00:00.000Z",
    "createAt": "2025-02-28T10:00:00.000Z",
    "profit": 100,
    "profitRatio": 10,
    "apy": 5,
    "holdTime": 7
  }
]
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数验证失败 |
| 401 | 未授权访问 |