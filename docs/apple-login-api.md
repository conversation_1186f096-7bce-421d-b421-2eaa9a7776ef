# 苹果授权登录 API 文档

## 概述

苹果授权登录功能基于 Apple 的 "Sign in with Apple" 服务，为 iOS 应用提供安全、便捷的第三方登录方式。

## 技术方案

- **验证方式**: identityToken 验证（推荐用于移动端）
- **JWT Token**: 双Token机制（Access Token + Refresh Token）
- **公钥缓存**: 本地数据库缓存24小时，提升验证性能
- **安全特性**: 签名验证、过期时间检查、发行者验证

## API 接口

### 1. 苹果登录

**接口地址**: `POST /api/apple/login`

**请求头**:
```
Content-Type: application/json
```

**请求参数**:
```json
{
    "identityToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "fullName": "John Doe",
    "email": "<EMAIL>"
}
```

**参数说明**:
- `identityToken` (必填): 苹果返回的身份令牌
- `fullName` (可选): 用户全名，仅首次登录时提供
- `email` (可选): 用户邮箱，仅首次登录时提供

**响应示例**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...",
        "tokenType": "Bearer",
        "expiresIn": 900,
        "user": {
            "id": 12345,
            "appleUserId": "000123.abc123def456.0789",
            "email": "<EMAIL>",
            "fullName": "John Doe",
            "isPrivateEmail": true,
            "emailVerified": true,
            "realUserStatus": 2
        },
        "isNewUser": false
    }
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "苹果登录验证失败：identityToken无效"
}
```

### 2. 健康检查

**接口地址**: `GET /api/apple/health`

**响应示例**:
```json
{
    "success": true,
    "message": "Apple Auth Service is running",
    "data": "OK"
}
```

## 使用流程

### iOS 客户端集成

1. **配置 Apple Developer**:
   - 启用 "Sign in with Apple" 功能
   - 配置 Bundle ID
   - 设置 entitlements

2. **获取 identityToken**:
```swift
import AuthenticationServices

func handleAppleSignIn() {
    let request = ASAuthorizationAppleIDProvider().createRequest()
    request.requestedScopes = [.fullName, .email]
    
    let authController = ASAuthorizationController(authorizationRequests: [request])
    authController.delegate = self
    authController.performRequests()
}

// 处理回调
func authorizationController(controller: ASAuthorizationController, 
                           didCompleteWithAuthorization authorization: ASAuthorization) {
    if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
        let identityToken = String(data: appleIDCredential.identityToken!, encoding: .utf8)!
        
        // 发送到后端验证
        loginWithApple(identityToken: identityToken, user: appleIDCredential)
    }
}
```

3. **调用后端API**:
```swift
func loginWithApple(identityToken: String, user: ASAuthorizationAppleIDCredential) {
    var fullName: String? = nil
    if let givenName = user.fullName?.givenName, let familyName = user.fullName?.familyName {
        fullName = "\(givenName) \(familyName)"
    } else if let givenName = user.fullName?.givenName {
        fullName = givenName
    } else if let familyName = user.fullName?.familyName {
        fullName = familyName
    }
    
    let loginRequest = AppleLoginRequest(
        identityToken: identityToken,
        fullName: fullName,
        email: user.email
    )
    
    // 发送请求到 /api/apple/login
}
```

### 后端处理流程

1. **接收请求**: 控制器接收客户端的登录请求
2. **验证Token**: 
   - 解析 JWT header 和 payload
   - 从缓存或苹果服务器获取公钥
   - 验证签名、过期时间、发行者等
3. **用户处理**:
   - 根据 `sub` 字段查询现有用户
   - 如果是新用户则创建用户记录
   - 更新用户信息（如需要）
4. **生成Token**: 生成应用的 JWT Token
5. **返回响应**: 返回登录结果和用户信息

## 配置说明

### 环境变量配置

```properties
# 苹果应用配置
apple.bundle.id=com.chenminjie.sea

# JWT配置
jwt.secret=your-secret-key-must-be-long-enough
jwt.access-token.expiration=900
jwt.refresh-token.expiration=2592000
```

### 数据库配置

确保已执行数据库迁移脚本：
- `V20250302000001__create_apple_auth_tables.sql`

## 安全考虑

1. **Token验证**: 严格验证苹果返回的 identityToken
2. **公钥缓存**: 定期更新苹果公钥，避免验证失败
3. **用户隐私**: 支持苹果隐私邮箱功能
4. **JWT安全**: 使用强密钥、合理的过期时间
5. **HTTPS**: 生产环境必须使用 HTTPS

## 错误处理

常见错误及解决方案：

1. **identityToken无效**:
   - 检查客户端是否正确获取token
   - 确认 Bundle ID 配置正确

2. **公钥获取失败**:
   - 检查网络连接
   - 确认苹果服务可达性

3. **签名验证失败**:
   - 检查时间同步
   - 确认公钥正确性

## 监控和日志

关键监控指标：
- 登录成功/失败率
- Token验证耗时
- 苹果API调用延迟
- 用户注册率

重要日志记录：
- 用户登录行为
- Token验证结果
- 异常情况详情

## 测试

运行单元测试：
```bash
mvn test -Dtest=AppleAuthServiceTest
```

测试覆盖场景：
- 新用户首次登录
- 现有用户登录
- 无效Token处理
- 异常情况处理 