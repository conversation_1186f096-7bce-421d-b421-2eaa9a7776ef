# Stock Filter API Documentation

## Filter Stocks V2

Filters stocks based on PE and PB percentile criteria with specified weighting methods.

### Endpoint

```
POST /api/stocks/filter
```

### Authentication

Required token parameter in query string.
- Token: `cmj`

### Request Parameters

#### Query Parameters
| Parameter | Required | Type   | Description |
|-----------|----------|--------|-------------|
| token     | Yes      | String | Authentication token. Must be "cmj" |

#### Request Body
```json
{
    "pePercentile5Y": number,      // Optional: 5-year PE-TTM percentile filter (0-100)
    "pePercentile10Y": number,     // Optional: 10-year PE-TTM percentile filter (0-100)
    "pbPercentile5Y": number,      // Optional: 5-year PB percentile filter (0-100)
    "pbPercentile10Y": number,     // Optional: 10-year PB percentile filter (0-100)
    "weightingMethods": string[]   // Required: Array of weighting methods ("ewpvo" and/or "mcw")
}
```

#### Validation Rules
- At least one filter type (PE or PB) must be enabled
- If provided, percentile values must be between 0 and 100
- weightingMethods array must contain at least one valid method ("ewpvo" or "mcw")

### Response

#### Success Response (200 OK)
```json
{
    "success": [
        {
            "code": string,                // Stock code
            "name": string,                // Stock name
            "metricType": string,          // Metric type
            "publishDate": string,         // Publish date (ISO format)
            "changeRate": number,          // Change rate
            "closePoint": number,          // Close point
            "peTtm5Y": number,            // PE-TTM (5-year)
            "peTtmPercentile5Y": number,  // PE-TTM percentile (5-year)
            "pb5Y": number,               // PB (5-year)
            "pbPercentile5Y": number,     // PB percentile (5-year)
            "psTtm5Y": number,            // PS-TTM (5-year)
            "psTtmPercentile5Y": number,  // PS-TTM percentile (5-year)
            "dividendYield5Y": number,    // Dividend yield (5-year)
            "peTtm10Y": number,           // PE-TTM (10-year)
            "peTtmPercentile10Y": number, // PE-TTM percentile (10-year)
            "pb10Y": number,              // PB (10-year)
            "pbPercentile10Y": number,    // PB percentile (10-year)
            "psTtm10Y": number,           // PS-TTM (10-year)
            "psTtmPercentile10Y": number, // PS-TTM percentile (10-year)
            "dividendYield10Y": number,   // Dividend yield (10-year)
            "updateTimestamp": number      // Last update timestamp
        }
    ],
    "failed": string[]  // Array of failed stock codes
}
```

#### Error Response (400 Bad Request)
- Invalid token
- Invalid request parameters

### Example

#### Request
```json
{
    "pePercentile5Y": 0.2,
    "pbPercentile5Y": 0.3,
    "weightingMethods": ["ewpvo", "mcw"]
}
```

#### Response
```json
{
    "success": [
                {
            "code": "399997",
            "name": "中证白酒",
            "metricType": "mcw",
            "publishDate": "2015-01-20T16:00:00.000+00:00",
            "changeRate": null,
            "closePoint": null,
            "peTtm5Y": 20.3467,
            "peTtmPercentile5Y": 0.1049,
            "pb5Y": 6.1366,
            "pbPercentile5Y": 0.1519,
            "psTtm5Y": 7.7610,
            "psTtmPercentile5Y": 0.1065,
            "dividendYield5Y": null,
            "peTtm10Y": null,
            "peTtmPercentile10Y": null,
            "pb10Y": null,
            "pbPercentile10Y": null,
            "psTtm10Y": null,
            "psTtmPercentile10Y": null,
            "dividendYield10Y": null,
            "updateTimestamp": 1742607205000
        }
    ],
    "failed": []
}
```

### Notes
- All percentile values should be provided as numbers between 0 and 100
- At least one of PE or PB filter must be specified
- Response includes both successful and failed stock codes
- All numeric values in the response are returned as BigDecimal for precision