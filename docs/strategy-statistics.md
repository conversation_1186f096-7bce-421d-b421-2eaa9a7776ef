# 策略统计功能说明

## 功能概述
提供策略统计信息的批量查询功能，包括总投入金额、网格卖出次数、累计收益等统计数据。

## API 接口

### 请求
```http
GET /api/v1/strategies/statistics?pageNumber={pageNumber}&pageSize={pageSize}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNumber | Integer | 是 | 页码，从1开始 |
| pageSize | Integer | 是 | 每页记录数，最大100 |

### 请求头
| 参数名 | 必填 | 说明 |
|--------|------|------|
| GSA-Session | 是 | 用户会话ID |

### 响应
```json
{
    "msg": "ok",
    "pageNum": 1,
    "pageSize": 10,
    "totalPages": 5,
    "result": [
        {
            "strategyId": 1001,
            "totalInvestment": 10000,
            "gridSellCount": 5,
            "totalProfit": 2000,
            "totalHoldingShares": 1000,
            "retainProfitShares": 50
        }
    ]
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| strategyId | Long | 策略ID |
| totalInvestment | Integer | 当前总投入金额 |
| gridSellCount | Integer | 网格卖出次数 |
| totalProfit | Integer | 累计收益 |
| totalHoldingShares | Integer | 总持有股数 |
| retainProfitShares | Integer | 预留利润股数 |

## 性能说明
- 平均响应时间：< 1秒
- P95响应时间：< 1.5秒
- 最大响应时间：< 2秒
- 并发支持：支持10个用户同时访问

## 测试说明

### 单元测试
```bash
# 运行单元测试
mvn test -Dtest=StrategyServiceImplTest

# 运行集成测试
mvn test -Dtest=StrategyStatisticsIntegrationTest

# 运行性能测试
mvn test -Dtest=StrategyStatisticsPerformanceTest

# 运行API测试
mvn test -Dtest=StrategyStatisticsApiTest
```

### 测试数据准备
```sql
-- 初始化测试数据
source src/test/resources/sql/init-test-strategies.sql

-- 清理测试数据
source src/test/resources/sql/init-empty-strategies.sql
```

## 注意事项
1. pageSize 不能超过100，超过会返回错误响应
2. 统计数据实时计算，可能会有微小延迟
3. 需要正确配置数据库索引以保证查询性能
4. 建议使用缓存优化高频访问场景

## 开发维护
1. 代码位置：`com.chenminjie.sea.presentation.controller.StrategyController`
2. 测试代码位置：`src/test/java/com/chenminjie/sea/`
3. SQL文件位置：`src/test/resources/sql/`
4. 配置文件位置：`src/test/resources/application.yml`

## 依赖项目
- Spring Boot 2.x
- MyBatis
- PageHelper
- H2 Database (测试)