# 策略统计功能监控配置

# Prometheus 指标配置
metrics:
  # 接口响应时间监控
  response_time:
    name: "strategy_statistics_response_time"
    help: "Strategy statistics API response time in milliseconds"
    labels: ["endpoint", "status"]
    buckets: [100, 200, 500, 1000, 2000, 5000]

  # 查询结果数量监控
  result_count:
    name: "strategy_statistics_result_count"
    help: "Number of strategies returned in statistics query"
    labels: ["user_id"]

  # 并发请求数监控
  concurrent_requests:
    name: "strategy_statistics_concurrent_requests"
    help: "Number of concurrent requests for strategy statistics"
    labels: ["endpoint"]

  # 错误次数监控
  error_count:
    name: "strategy_statistics_error_count"
    help: "Number of errors in strategy statistics queries"
    labels: ["error_type"]

# Grafana 告警规则
alerts:
  # 响应时间告警
  response_time:
    warning:
      threshold: 1000  # ms
      duration: "5m"
      description: "Strategy statistics API average response time > 1s in last 5 minutes"
    critical:
      threshold: 2000  # ms
      duration: "5m"
      description: "Strategy statistics API average response time > 2s in last 5 minutes"

  # 错误率告警
  error_rate:
    warning:
      threshold: 0.01  # 1%
      duration: "5m"
      description: "Strategy statistics API error rate > 1% in last 5 minutes"
    critical:
      threshold: 0.05  # 5%
      duration: "5m"
      description: "Strategy statistics API error rate > 5% in last 5 minutes"

  # 并发请求告警
  concurrent_requests:
    warning:
      threshold: 50
      duration: "1m"
      description: "Strategy statistics concurrent requests > 50 in last 1 minute"
    critical:
      threshold: 100
      duration: "1m"
      description: "Strategy statistics concurrent requests > 100 in last 1 minute"

# 日志监控配置
logging:
  # 错误日志关键字
  error_keywords:
    - "Failed to get strategy statistics"
    - "Database connection timeout"
    - "Query execution timeout"
    - "Invalid parameter"
  
  # 性能日志模式
  performance_patterns:
    - pattern: "Slow query detected"
      threshold: 1000  # ms
    - pattern: "High memory usage"
      threshold: 80    # percent

# 监控面板配置
dashboard:
  panels:
    - title: "Response Time Distribution"
      type: "histogram"
      metric: "strategy_statistics_response_time"
      
    - title: "Error Rate"
      type: "graph"
      metric: "strategy_statistics_error_count"
      
    - title: "Concurrent Requests"
      type: "gauge"
      metric: "strategy_statistics_concurrent_requests"
      
    - title: "Query Results"
      type: "graph"
      metric: "strategy_statistics_result_count"

# 告警通知配置
notifications:
  channels:
    - type: "email"
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    - type: "webhook"
      url: "http://alert-service/api/v1/notify"
    
    - type: "slack"
      channel: "#monitoring-alerts"

# 监控数据保留策略
retention:
  metrics_storage_days: 30
  logs_storage_days: 7
  dashboard_snapshot_count: 100