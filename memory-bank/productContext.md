# Sea 项目上下文

## 项目概述
Sea是一个金融交易相关的应用系统，使用Java Spring Boot开发，采用DDD架构设计。

## 核心概念
- TradeLog: 交易日志，记录用户的交易信息
- Session: 用户会话管理
- Authentication: 基于Filter的认证机制

## 项目架构
- 分层架构(DDD):
  - Presentation层: 控制器和请求/响应对象
  - Application层: 业务服务
  - Domain层: 领域模型和核心业务逻辑
  - Data层: 数据访问

## Memory Bank文件说明
- productContext.md: 项目整体上下文和重要信息
- activeContext.md: 当前开发会话的上下文信息
- progress.md: 开发进度追踪
- decisionLog.md: 架构决策记录