# Decision Log

This file records architectural and implementation decisions using a list format.
2025-03-22 20:33:48 - Log of updates made.

## Decision

* Extend stock index API to support multiple metric types
* Add metricType field to StockIndex model to distinguish different metric types
* Maintain backward compatibility with existing requests

## Rationale 

* Allow more flexible querying of different metric types in a single request
* Preserve data source identification in the response
* Ensure existing clients continue to work without modification

## Implementation Details

* Modify StockIndexResponse.java to add metricType field
* Update StockController to handle comma-separated metric types
* Update service layer to process multiple metric types
* Keep default behavior for single metric type requests

# Stock Percentile Filter Feature

## Decision
- 创建新的独立接口 `/stocks/filter` 实现股票百分位筛选功能，而不是在现有 `/index` 接口中添加筛选功能

## Rationale
- 遵循单一职责原则，新接口专注于百分位筛选功能
- 保持接口参数简洁清晰，提高可维护性
- 便于未来扩展更多筛选条件
- 避免使现有/index接口过于复杂

## Implementation Details
- 接口路径：GET /stocks/filter
- 参数：
  * fiveYearPercentile：五年百分位阈值
  * tenYearPercentile：十年百分位阈值
  * token：鉴权token
- 复用 StockIndexResponse 作为响应结构