# 网格策略统计系统

## 项目概述
提供网格交易策略的统计功能，包括总投入金额、网格卖出次数、累计收益等数据的批量查询功能。

## 快速开始

### 环境要求
- JDK 11+
- Maven 3.6+
- MySQL 5.7+
- Redis 6.0+

### 构建部署
```bash
# 1. 克隆代码
git clone https://github.com/your-repo/sea.git

# 2. 编译打包
mvn clean package

# 3. 运行测试
mvn test

# 4. 部署运行
java -jar target/sea.jar
```

### 配置说明
参见：`src/main/resources/application.yml`

## 项目文档

### 技术文档
- [API 接口文档](docs/strategy-statistics.md)
- [数据库设计](src/main/resources/db/migration/V20250302000000__add_strategy_statistics_indexes.sql)
- [监控配置](config/monitoring/strategy-statistics-monitoring.yml)

### 部署文档
- [部署指南](docs/deployment/strategy-statistics-deployment.md)
- [性能优化指南](docs/performance/strategy-statistics-optimization.md)
- [项目总结](docs/summary.md)

## 主要功能

### 策略统计
- 支持批量查询策略统计数据
- 提供完整的分页功能
- 实现并发查询优化
- 支持实时数据统计

### 性能特性
- 响应时间 < 1秒
- 支持并发访问
- 可水平扩展
- 完整监控支持

## 开发指南

### 代码结构
```
src/
├── main/
│   ├── java/
│   │   └── com/chenminjie/sea/
│   │       ├── application/    # 应用服务层
│   │       ├── domain/        # 领域层
│   │       ├── presentation/  # 表现层
│   │       └── data/         # 数据访问层
│   └── resources/
│       ├── db/               # 数据库脚本
│       └── config/          # 配置文件
└── test/                    # 测试代码
    ├── java/               
    └── resources/          # 测试资源
```

### 开发流程
1. 创建功能分支
2. 编写测试用例
3. 实现功能代码
4. 执行单元测试
5. 提交代码审查
6. 合并主分支

### 测试说明
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify -P integration-test

# 运行性能测试
mvn verify -P performance-test
```

## 监控运维

### 监控指标
- 接口响应时间
- 并发请求数
- 错误率统计
- 资源使用情况

### 告警配置
- 响应时间超限
- 错误率过高
- 资源不足
- 系统异常

### 运维命令
```bash
# 健康检查
curl http://localhost:8080/actuator/health

# 查看指标
curl http://localhost:8080/actuator/metrics

# 查看性能
curl http://localhost:8080/actuator/prometheus
```

## 常见问题

### Q1: 如何优化查询性能？
参见：[性能优化指南](docs/performance/strategy-statistics-optimization.md)

### Q2: 如何处理并发问题？
参见：[部署指南](docs/deployment/strategy-statistics-deployment.md)

### Q3: 如何进行性能测试？
参见：[性能测试说明](docs/performance/strategy-statistics-optimization.md#5-性能测试)

## 维护团队
- 技术负责人：[姓名]
- 开发团队：[团队]
- 运维团队：[团队]

## License
MIT License

## 更新日志
- 2025/03/02: 初始版本发布
- 2025/03/02: 添加完整的测试覆盖
- 2025/03/02: 完善监控和运维支持