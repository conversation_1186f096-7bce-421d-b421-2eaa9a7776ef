drop table if exists grid_strategy;
CREATE TABLE `grid_strategy`
(
    `id`                    int unsigned NOT NULL AUTO_INCREMENT,
    `open_id`               varchar(64) NOT NULL,
    `name`                  varchar(64) NOT NULL,
    `interval`              int unsigned NOT NULL,
    `target_price`          int unsigned NOT NULL,
    `amount`                int unsigned NOT NULL,
    `max_fall`              int unsigned NOT NULL,
    `buy_strategy`          int unsigned NOT NULL,
    `sell_strategy`         int unsigned NOT NULL,
    `sell_grid`             int unsigned DEFAULT NULL,
    `buy_grid`              int unsigned DEFAULT NULL,
    `sell_price`            int unsigned DEFAULT NULL,
    `buy_price`             int unsigned DEFAULT NULL,
    `incremental_buy_ratio` int unsigned DEFAULT NULL,
    `medium_large_switch`   tinyint      DEFAULT NULL,
    `medium_interval`       int unsigned DEFAULT NULL,
    `large_interval`        int unsigned DEFAULT NULL,
    `union_id`              varchar(64)  DEFAULT NULL,
    `create_at`             timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`             timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `is_delete`             int          DEFAULT '0',
    `remainder_shares`      int          DEFAULT '0',
    `code`                  varchar(255) DEFAULT '',
    PRIMARY KEY (`id`)
);

drop table if exists grid;
CREATE TABLE `grid`
(
    `id`                      int unsigned NOT NULL AUTO_INCREMENT,
    `strategy_id`             int unsigned NOT NULL,
    `grid_type`               int          NOT NULL,
    `level`                   int          NOT NULL,
    `hold_shares`             int          NOT NULL,
    `theoretical_buy_price`   int          NOT NULL,
    `theoretical_buy_shares`  int          NOT NULL,
    `theoretical_sell_price`  int          NOT NULL,
    `theoretical_sell_shares` int          NOT NULL,
    `buy_price`               int          NOT NULL,
    `status`                  int          NOT NULL,
    `create_at`               timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`               timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `buy_at`                  timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `open_id`                 varchar(255) NOT NULL,
    `is_delete`               bit(1) DEFAULT b'0',
    `trigger_amount`          int    DEFAULT NULL,
    PRIMARY KEY (`id`)
);

drop table if exists session;
CREATE TABLE `session`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `session_key` varchar(64) NOT NULL,
    `open_id`     varchar(64) NOT NULL,
    `union_id`    varchar(64) DEFAULT NULL,
    `create_at`   timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`   timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `session_key` (`session_key`)
);

drop table if exists stock;
CREATE TABLE `stock`
(
    `id`     bigint NOT NULL AUTO_INCREMENT,
    `code`   varchar(255) DEFAULT NULL,
    `name`   varchar(255) DEFAULT NULL,
    `type`   int          DEFAULT NULL COMMENT '0: stock\n1: fund\n2: index',
    `market` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code_market` (`code`,`market`),
    KEY      `idx_code` (`code`),
    KEY      `idx_code_prefix` (`code`(10))
);

ALTER TABLE `stock`
ADD COLUMN `max_value` varchar(64) DEFAULT NULL,
ADD COLUMN `min_value` varchar(64) DEFAULT NULL;


drop table if exists trade_log;
CREATE TABLE `trade_log`
(
    `id`                 int unsigned NOT NULL AUTO_INCREMENT,
    `strategy_id`        int unsigned NOT NULL,
    `grid_id`            int unsigned DEFAULT NULL,
    `grid_type`          int          NOT NULL,
    `level`              int    DEFAULT NULL,
    `trade_type`         int          NOT NULL,
    `trade_shares`       int          NOT NULL,
    `trade_price`        int          NOT NULL,
    `reminder_shares`    int    DEFAULT NULL,
    `theoretical_price`  int    DEFAULT NULL,
    `theoretical_shares` int    DEFAULT NULL,
    `trade_at`           timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `create_at`          timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `open_id`            varchar(255) NOT NULL,
    `is_delete`          bit(1) DEFAULT b'0',
    `profit`             int    DEFAULT '0',
    PRIMARY KEY (`id`)
);

drop table if exists flexible_trade_log;
CREATE TABLE flexible_trade_log
(
      `id` int unsigned NOT NULL AUTO_INCREMENT,
      `strategy_id` int unsigned NOT NULL,
      `trade_type` int NOT NULL,
      `trade_shares` int NOT NULL,
      `trade_price` int NOT NULL,
      `trade_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      `open_id` varchar(255) NOT NULL,
      index(`strategy_id`, `open_id`),
      PRIMARY KEY (`id`)
);

drop table if exists stock_index;
CREATE TABLE `stock_index` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `index_code` varchar(24) NOT NULL COMMENT '指数代码',
  `index_name` varchar(255) NOT NULL COMMENT '指数名称',
  `publish_date` date NOT NULL COMMENT '发布时间',
  `change_rate` decimal(10,4) DEFAULT NULL COMMENT '涨跌幅',
  `close_point` decimal(10,2) DEFAULT NULL COMMENT '收盘点位',
  `period_year` int DEFAULT NULL COMMENT '数据周期年份',
  `metric_type` varchar(24) DEFAULT NULL COMMENT '指标类型',
  `pe_ttm` decimal(10,4) DEFAULT NULL COMMENT 'PE-TTM(当前值)',
  `pe_ttm_percentile` decimal(10,4) DEFAULT NULL COMMENT 'PE-TTM(分位点%)',
  `pb` decimal(10,4) DEFAULT NULL COMMENT 'PB(当前值)',
  `pb_percentile` decimal(10,4) DEFAULT NULL COMMENT 'PB(分位点%)',
  `ps_ttm` decimal(10,4) DEFAULT NULL COMMENT 'PS-TTM(当前值)',
  `ps_ttm_percentile` decimal(10,4) DEFAULT NULL COMMENT 'PS-TTM(分位点%)',
  `dividend_yield` decimal(10,4) DEFAULT NULL COMMENT '股息率',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code_period_year_metric_type` (`index_code`,`period_year`,`metric_type`),
  KEY `idx_code_prefix` (`index_code`(10)),
  KEY `idx_name_prefix` (`index_name`(10))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='指数估值数据表';

-- CREATE TABLE `gtt_user`
-- (
--     `id`      int unsigned NOT NULL AUTO_INCREMENT,
--     `apple_id` varchar(128) NOT NULL,
--     `full_name` varchar(128) DEFAULT NULL,
--     `email` varchar(128) DEFAULT NULL,
--     `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
--     `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
--     PRIMARY KEY (`id`),
--     UNIQUE KEY `apple_id` (`apple_id`)
-- );
--
-- CREATE TABLE `gtt_session`
-- (
--     `id`      int unsigned NOT NULL AUTO_INCREMENT,
--     `session_key` varchar(64) NOT NULL,
--     `user_id` int unsigned NOT NULL,
--     `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
--     `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
--     `expire_at` timestamp NULL,
--     `is_valid` bit(1) DEFAULT b'0'
-- );